# -*- coding: utf-8 -*-

cookie = """
buvid3=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; b_nut=1725987880; _uuid=E104A4F5E-51B4-C8D2-7794-165EC102DDB8E81048infoc; enable_web_push=DISABLE; buvid4=9871B804-287B-FA73-2F90-1BD647880D9681439-024091017-qdXK1QUQFKw0pLifwa9zNQ%3D%3D; rpdid=|(um|kmkJ|~Y0J'u~klm))|~); DedeUserID=224780442; DedeUserID__ckMd5=7c2f92cdd5fd4d6a; hit-dyn-v2=1; LIVE_BUVID=AUTO9717315048529878; buvid_fp_plain=undefined; CURRENT_QUALITY=80; go-back-dyn=0; enable_feed_channel=ENABLE; fingerprint=b1426b43418012b14124fcc3db8ee047; buvid_fp=b1426b43418012b14124fcc3db8ee047; PVID=1; header_theme_version=OPEN; theme-tip-show=SHOWED; theme-avatar-tip-show=SHOWED; home_feed_column=5; bp_t_offset_224780442=1097154339270557696; CURRENT_FNVAL=4048; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3OTU5ODgsImlhdCI6MTc1NDUzNjcyOCwicGx0IjotMX0.arBX5uz0kRpuL8wgkqZSk5uHoYg3n025vn6GIXmx8uQ; bili_ticket_expires=1754795928; b_lsid=38A75312_19883A7B294; SESSDATA=5df2ca95%2C1770111423%2C68c6a%2A81CjDIshicBrnc3xXzjYGyjKe7jAVfzvsG3kq7iwWKCCSvVEm78b5yth9gEVl_yhOG_2MSVkJWbi1STE5hN244UC1yakpWWWRuQnhuaG5ZWmxfeWJQTzVXQ2wzank4LXVIOEp1RTZYejdFWXNMMFhQQ0dzdTl1UU5CVFdkVzNvUmtGTW8tRDFiQ2dnIIEC; bili_jct=15f6c87a0f02af7179dd932fcd53867e; sid=72frdmll; browser_resolution=1492-1338
"""


# cookie = """
# buvid3=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; b_nut=1725987880; _uuid=E104A4F5E-51B4-C8D2-7794-165EC102DDB8E81048infoc; enable_web_push=DISABLE; buvid4=9871B804-287B-FA73-2F90-1BD647880D9681439-024091017-qdXK1QUQFKw0pLifwa9zNQ%3D%3D; header_theme_version=CLOSE; rpdid=|(um|kmkJ|~Y0J'u~klm))|~); DedeUserID=224780442; DedeUserID__ckMd5=7c2f92cdd5fd4d6a; hit-dyn-v2=1; LIVE_BUVID=AUTO9717315048529878; buvid_fp_plain=undefined; CURRENT_QUALITY=80; go-back-dyn=0; enable_feed_channel=ENABLE; SESSDATA=d3fbed55%2C1765168820%2Cdcb92%2A61CjA7J7XcYFzx-5zUzoNz5H9e3NYFrzTEFu6iPkVn6fROaIw1lg25SSje98YQupTTxU0SVkNwMV9jM25SbVVrZGhfZDYxWEo3dXNOMERFZ3Q0STMyeEY5VVhLZTlxOTQtZzBjb3pNdDFyc25GZWNHRFRobG5qeXhGQ21lbUVTZDMtc0dlY1VsbXFRIIEC; bili_jct=73b892971dfc1647574d073f474dbe2e; fingerprint=9c23685b4ff99eb4b022de7784c57446; timeMachine=0; sid=71e9ino0; share_source_origin=WEIXIN; bsource=share_source_weixinchat; b_lsid=1D3CF4F5_197681738F8; CURRENT_FNVAL=2000; home_feed_column=5; browser_resolution=3165-1598; buvid_fp=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; bp_t_offset_224780442=1077874815194365952; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTAwNTk4ODgsImlhdCI6MTc0OTgwMDYyOCwicGx0IjotMX0.QKaE_Er-rWQ0U0o5xcaLcF5Nb8kignoAVeTFb8vPD2E; bili_ticket_expires=1750059828
# """

# 需要提取的字段及其对应的cookie名
fields = {
    "SESSDATA": "SESSDATA",
    "BILI_JCT": "bili_jct",
    "B_NUT": "b_nut",
    "SID": "sid",
    "BUVID3": "buvid3",
    "BUVID4": "buvid4",
    "DEDEUSERID": "DedeUserID",
}

# 解析cookie为字典
cookie_dict = {}
for item in cookie.split(";"):
    if "=" in item:
        k, v = item.strip().split("=", 1)
        cookie_dict[k] = v

# 提取并输出
output = []
for key, cookie_name in fields.items():
    value = cookie_dict.get(cookie_name, "")
    output.append(f'{key} = "{value}"')

# 输出为text
result = "\n".join(output)
print(result)
