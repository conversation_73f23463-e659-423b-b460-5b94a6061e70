import math
import os
import re
import datetime
import asyncio
import json
from typing import Dict
import a<PERSON>ie<PERSON> as tb
import asyncpg
from bilibili_api import user, Credential, live
from bilibili_api import exceptions as bilibili_exceptions
import aiohttp

from backend.tools.query_user_data import query_recent_info
from const import (
    BUVID4,
    SESSDATA,
    BILI_JCT,
    BUVID3,
    DEDEUSERID,
    BDUSS,
)
from sql import sql_const
import sql.fatal_sql_const as fatal_sql

import backend.utils as U
from backend.tools.fetch_comments import fetch_single_dynamic_comments, fetch_single_video_comments
from backend.utils.tieba_crawl import tieba_crawler_thread
from backend.actions.relationship_summarize import RelationshipSummarize
from backend.actions.comment_sum import CommentSummarize
from backend.tools.discuss_utils import senta_sentences_sentiment_analysis
from backend.tools.fetch_video_info_tools import *  # noqa: F403
from backend.actions.reasoning_of_fans import ReasoningOfFans

from logger import logger


def get_nested_value(d, keys, default=None):
    for key in keys:
        d = d.get(key)
        if d is None:
            return default
    return d


# Delay time
DYNAMICS_FETCH_DELAY = 10
VIDEO_FETCH_DELAY = 3
ALL_VIDEO_INFO_DELAY = 1
VIDEO_AI_CONCLUSION_DELAY = 1
TIEBA_WHOLE_DELAY = 1
TIEBA_THREAD_DELAY = 0.5
DAHANGHAI_LIST_FETCH_LIST_DELAY = 2
FOLLOWER_LIST_FETCH_LIST_DELAY = 10
FETCH_HISTORY_F_AND_D_DELAY = 10
USER_CUR_DATE_DELAY = 8

SQL_FAIL_DELAY = 3

DELETE_UNFOLLOWED_FANS = False # 删除不再关注的粉丝

# POOL_SIZE
TIEBA_INSERT_POOL_SIZE = 50
COMMENT_INSERT_POOL_SIZE = 50
FOLLOWER_REVIEW_INSERT_POOL_SIZE = 50

FETCH_FOLLOWERS_MAX_PAGES = 20

AI_GEN_MODEL = "hunyuan-large-longcontext" # qwen3-235b-a22b:free
# AI_GEN_MODEL = "qwen3-235b" # 
# AI_GEN_MODEL = "qwen3-235b-a22b:free" # 

class UserInfoServer:

    def __init__(self, char, credential=None, db_conn=None):
        self.char = char

        if credential:
            self.credential = credential
        else:
            self.credential = Credential(
                sessdata=SESSDATA,
                bili_jct=BILI_JCT,
                buvid3=BUVID3,
                dedeuserid=DEDEUSERID,
                buvid4=BUVID4,
            )

        self.should_close_conn = False
        self.conn = db_conn

        self.char_zh = U.get_zh_role_name(char)
        self.mid = self._get_user_mid_sync()
        
        self.u = user.User(self.mid, self.credential)
        
        self.liveid = None
        self.liveroom = None
        self.recent_info = None

    def __del__(self):

        if hasattr(self, "liveroom") and self.liveroom:
            self.liveroom = None

    async def _execute_sql(self, sql, params=None, fetch_one=False, fetch_all=False, executemany_params=None, max_retries=3):
        """
        辅助函数执行 SQL，带有智能重试逻辑，使用连接池 (异步版本)
        Helper function to execute SQL with smart retry logic, using a connection pool (asynchronous version).
        
        Args:
            sql: The SQL statement to execute.
            params: Parameters for a single SQL execution.
            fetch_one: Whether to fetch a single row result.
            fetch_all: Whether to fetch all results.
            executemany_params: A list of parameter tuples for executemany.
            max_retries: Maximum number of retries.
            
        Returns:
            Query results or None (if execution fails).
        """
        from backend.utils.db_pool import get_connection

        retriable_errors = (
            asyncpg.exceptions.PostgresConnectionError,
            asyncpg.exceptions.InterfaceError,
            asyncpg.exceptions.ConnectionDoesNotExistError,
        )
        
        retry_count = 0
        last_exception = None # To store the exception if all retries fail

        while retry_count <= max_retries:
            try:
                async with get_connection() as db_conn:
                    if executemany_params:
                        await db_conn.executemany(sql, executemany_params)
                        return None # Explicitly return None for executemany, consistent with DML
                    else:
                        if fetch_one:
                            result = await db_conn.fetchrow(sql, *params if params else [])
                            return result # asyncpg.Record or None
                        elif fetch_all:
                            result = await db_conn.fetch(sql, *params if params else [])
                            return result # list of asyncpg.Record
                        else:
                            # For INSERT/UPDATE/DELETE or DDL
                            await db_conn.execute(sql, *params if params else [])
                            return None

            except retriable_errors as e:
                last_exception = e
                retry_count += 1
                if retry_count <= max_retries:
                    delay = SQL_FAIL_DELAY * (2 ** (retry_count - 1))
                    logger.warning(
                        f"Encountered retriable error during SQL execution (Attempt {retry_count}/{max_retries}): {e}\n"
                        f"SQL: {sql}\n"
                        f"Retrying in {delay} seconds"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        f"Max retries ({max_retries}) reached for SQL execution, giving up: {e}\n"
                        f"SQL: {sql}"
                    )
                    return None # Propagate failure after retries
            
            except asyncpg.exceptions.DuplicateTableError as e:
                # Check if this is a CREATE TABLE IF NOT EXISTS statement
                if re.match(r"^\s*CREATE\s+TABLE\s+IF\s+NOT\s+EXISTS", sql, re.IGNORECASE):
                    logger.warning(
                        f"Table already exists, which is normally expected and handled by 'CREATE TABLE IF NOT EXISTS'. "
                        f"This warning indicates the command still raised DuplicateTableError. SQL: {sql}. Error: {e}"
                    )
                    return None 
                else:
                    logger.error(
                        f"Unhandled DuplicateTableError error during SQL execution: {e}\n"
                        f"SQL: {sql}"
                    )
                    last_exception = e # Store exception before returning None or re-raising
                    return None # Or consider re-raising if this should be fatal
            
            except asyncpg.exceptions.PostgresError as e: # Catch other PostgreSQL specific errors
                logger.error(
                    f"Non-retriable error during SQL execution: {e}\n"
                    f"SQL: {sql}"
                )
                last_exception = e
                return None

        # If loop finishes due to max_retries, last_exception will be set
        if last_exception:
            logger.error(f"SQL execution failed after {max_retries} retries with last error: {last_exception}")
        return None

    def _get_user_mid_sync(self):
        uid = U.get_vup_uid_by_short_name(self.char_zh)
        if uid is None:
            raise Exception(f"Character {self.char_zh} not found in vups.json")
        return str(uid)

    async def async_init(self):
        """Asynchronously initializes parts of the server that require async operations."""
        if self.liveid is not None: # Already initialized
            logger.info(f"UserInfoServer for {self.char_zh} already async-initialized.")
            return

        logger.info(f"Async initializing UserInfoServer for {self.char_zh}...")
        await self.create_table() # Moved create_table call here
        try:
            live_id_result = await self.get_live_id()
            if live_id_result:
                self.liveid = live_id_result
                if self.liveid: # Ensure liveid is not None or empty before int() conversion
                    try:
                        self.liveroom = live.LiveRoom(int(self.liveid), credential=self.credential)
                        logger.info(f"UserInfoServer for {self.char_zh} async-initialized successfully with liveid {self.liveid}.")
                    except ValueError:
                        logger.error(f"Failed to initialize LiveRoom for {self.char_zh}: liveid '{self.liveid}' is not a valid integer.")
                        self.liveid = None # Reset if invalid
                        self.liveroom = None
                    except Exception as e_lr:
                        logger.error(f"Failed to initialize LiveRoom for {self.char_zh} with liveid {self.liveid}: {e_lr}")
                        self.liveroom = None
                else:
                    logger.error(f"Failed to async-initialize UserInfoServer for {self.char_zh}: get_live_id returned None or empty.")
            else:
                logger.error(f"Failed to async-initialize UserInfoServer for {self.char_zh}: get_live_id returned None.")
        except Exception as e:
            logger.error(f"Error during async_init for {self.char_zh}: {e}")
            self.liveid = None
            self.liveroom = None

    async def _ensure_table_exists(self, create_sql_func):
        """确保表存在 (using self.conn) by executing CREATE TABLE IF NOT EXISTS."""
        create_sql = create_sql_func(self.mid)
        table_name_for_log = "Unknown Table (extract failed)"
        try:
            table_name_match = re.search(r"CREATE TABLE IF NOT EXISTS\s+([^\s(]+)", create_sql, re.IGNORECASE)
            if table_name_match:
                table_name_for_log = table_name_match.group(1)
            
            logger.info(f"确保表 {table_name_for_log} (通过 CREATE IF NOT EXISTS 在 self.conn 上) 存在...")
            await self._execute_sql(create_sql)
            logger.info(f"表 {table_name_for_log} (通过 CREATE IF NOT EXISTS 在 self.conn 上) 已确保存在.")
        except Exception as e:
            logger.error(f"尝试确保表 {table_name_for_log} 存在时出错 (在 _ensure_table_exists 的外层): {e}\nSQL: {create_sql}")

    async def _batch_insert_data(self, table_name, insert_sql, items, ordered_keys): # Added ordered_keys
        """辅助函数批量插入数据，使用 _execute_sql"""
        if not items:
            return
        
        # Convert list of dicts to list of tuples
        # Ensure all keys in ordered_keys exist in each dictionary d, or handle missing keys (e.g., with d.get(key))
        items_as_tuples = [tuple(d.get(key) for key in ordered_keys) for d in items]

        logger.info(f"准备向数据库表 {table_name} 插入 {len(items_as_tuples)} 条数据...")
        try:
            await self._execute_sql(insert_sql, executemany_params=items_as_tuples) # Pass tuples
            logger.info(f"成功将 {len(items_as_tuples)} 条数据插入 {table_name}")
        except Exception as insert_err:
            logger.error(f"插入数据到数据库表 {table_name} 时出错: {insert_err}")

    async def table_exists(self, table_name, schema_name="public"):
        full_table_name = f"{schema_name}.{table_name}"
        try:
            result = await self._execute_sql(fatal_sql.exist_table_sql, (full_table_name,), fetch_one=True)
            return result[0] if result else False
        except Exception as e:
            logger.error(f"检查表 {full_table_name} 是否存在时出错: {e}")
            return False

    async def create_table(self): 
        try:
            await self._execute_sql(fatal_sql.create_current_stat_table_sql)
            await self._execute_sql(fatal_sql.create_dynamics_table_sql)
            await self._execute_sql(fatal_sql.create_videos_table_sql)
            await self._execute_sql(fatal_sql.create_user_info_table_sql)
            await self._execute_sql(fatal_sql.create_fans_medal_rank_table_sql)
            await self._execute_sql(fatal_sql.create_video_day_data_table_sql)
            await self._execute_sql(fatal_sql.create_dahanghai_list_table_sql)
            await self._execute_sql(fatal_sql.create_followers_list_table_sql)
            await self._execute_sql(fatal_sql.create_ai_gen_table_sql)
            await self._execute_sql(fatal_sql.create_tieba_threads_table_sql)
            await self._execute_sql(fatal_sql.create_follower_review_table_sql)
            logger.info("所有数据库表已确保存在。 (async)")
        except Exception as e:
            logger.error(f"创建数据库表时出错 (async): {e}")

    async def get_user_face(self):
        pass

    async def fetch_user_current_stat(self):
        """
        获取当前用户统计数据（观看、点赞、充电）并存入数据库
        Get current user statistics (view, like, charge) and store them in the database
        :return: None
        """

        current_dt = datetime.datetime.now().replace(minute=0, second=0, microsecond=0)
        current_ts = int(current_dt.timestamp())

        try:
            logger.info(f"获取 {self.char_zh} 的当前统计数据")
            stat = await self.u.get_up_stat()
            logger.info(f"打印 {self.char_zh} 的当前统计数据：{stat}")
            try:
                elec_info = await self.u.get_elec_user_monthly()
                elec_num = elec_info.get("count", 0)
            except Exception as e:
                logger.warning(f"获取 {self.char_zh} 充电信息失败: {e}")
                elec_num = 0
            follower_info = await self.u.get_relation_info()
            follower_num = follower_info.get("follower", 0)
            dahanghai_num = 0
            try:
                if not self.liveroom:
                    if self.liveid is None: await self.async_init()
                    if self.liveid:
                        self.liveroom = live.LiveRoom(
                            int(self.liveid), credential=self.credential
                        )
                    else:
                        logger.warning(f"Live ID for {self.char_zh} is None, cannot fetch Dahanghai number.")
                        dahanghai_num = 0
                
                if self.liveroom:
                    dahanghai_reply = await self.liveroom.get_dahanghai()
                    dahanghai_num = dahanghai_reply.get("info", {}).get("num", 0)
            except Exception as live_err:
                logger.warning(f"获取 {self.char_zh} 大航海数失败: {live_err}")

            stat_data_dict = {
                "uid": self.mid,
                "name": self.char_zh,
                "timestamp": current_ts,
                "datetime": current_dt, # datetime object
                "follower_num": follower_num,
                "dahanghai_num": dahanghai_num,
                "video_total_num": stat.get("archive", {}).get("view", 0),
                "article_total_num": stat.get("article", {}).get("view", 0),
                "likes_total_num": stat.get("likes", 0),
                "elec_num": elec_num,
            }
            
            # Order for insert_current_stat_table_sql:
            # uid, name, timestamp, datetime, follower_num, dahanghai_num,
            # video_total_num, article_total_num, likes_total_num, elec_num
            stat_data_tuple = (
                stat_data_dict["uid"],
                stat_data_dict["name"],
                stat_data_dict["timestamp"],
                stat_data_dict["datetime"],
                stat_data_dict["follower_num"],
                stat_data_dict["dahanghai_num"],
                stat_data_dict["video_total_num"],
                stat_data_dict["article_total_num"],
                stat_data_dict["likes_total_num"],
                stat_data_dict["elec_num"],
            )

            await self._execute_sql(fatal_sql.insert_current_stat_table_sql, stat_data_tuple)
            logger.info(f"已将 {self.char_zh} 的当前统计数据存入数据库")

        except Exception as e:
            logger.error(f"获取或存储 {self.char_zh} 当前统计数据时出错: {e}")

        await asyncio.sleep(USER_CUR_DATE_DELAY)

    async def fetch_fans_medal_rank(self):
        """
        EG:
        {
            'uid': 486553605,
            'uname': '钉钩鱼早点睡',
            'face': 'https://i0.hdslb.com/bfs/face/a108d60f0986054723fcddfdcd097055d6c9bfe9.jpg',
            'rank': 1,
            'medal_name': '瞳星结',
            'level': 33,
            'color': 7996451,
            'target_id': 401315430,
            'special': '',
            'isSelf': 0,
            'guard_level': 1,
            'medal_color_start': 7996451,
            'medal_color_end': 15304379,
            'medal_color_border': 16771156,
            'is_lighted': 1
        }
        """
        if not self.liveroom:
            if self.liveid is None: await self.async_init()
            if self.liveid: 
                self.liveroom = live.LiveRoom(int(self.liveid), credential=self.credential)
            else:
                logger.error(f"Live ID for {self.char_zh} is None, cannot fetch fans medal rank.")
                return []
        
        if not self.liveroom: # Double check if liveroom could not be initialized
            logger.error(f"LiveRoom for {self.char_zh} could not be initialized, cannot fetch fans medal rank.")
            return []

        try:
            reply = await self.liveroom.get_fans_medal_rank()
            rank_list_data = reply.get("list", [])

            if rank_list_data:
                current_dt = datetime.datetime.now()
                rank_list_json = json.dumps(rank_list_data, ensure_ascii=False)

                data_to_insert_dict = {
                    "uid": self.mid,
                    "name": self.char_zh,
                    "liveid": self.liveid,
                    "datetime": current_dt.replace(hour=0, minute=0, second=0, microsecond=0),
                    "rank_list": rank_list_json,
                }
                
                # Order for insert_fans_medal_rank_table_sql: uid, name, liveid, datetime, rank_list
                data_to_insert_tuple = (
                    data_to_insert_dict["uid"],
                    data_to_insert_dict["name"],
                    data_to_insert_dict["liveid"],
                    data_to_insert_dict["datetime"],
                    data_to_insert_dict["rank_list"],
                )
                
                await self._execute_sql(fatal_sql.insert_fans_medal_rank_table_sql, data_to_insert_tuple)
                logger.info(f"已将 {self.char_zh} 的粉丝牌排行数据存入数据库")

            return rank_list_data
        except Exception as e:
            logger.error(f"获取或存储 {self.char_zh} 粉丝牌排行失败: {e}")
            return []

    async def fetch_user_dynamics(self):
        """获取用户动态"""
        offset = ""
        # 1. get existing dynamic_id from database
        existing_ids_sql = f"SELECT dynamic_id FROM dynamics_table WHERE uid = $1"
        existing_results = await self._execute_sql(
            existing_ids_sql, (self.mid,), fetch_all=True
        )
        existing_ids = (
            set(row["dynamic_id"] for row in existing_results)
            if existing_results
            else set()
        )
        logger.info(f"数据库中已存在 {len(existing_ids)} 条 {self.char_zh} 的动态 ID")

        new_dynamics_count = 0
        # 2. 获取动态
        while True:
            try:
                logger.info(f"获取动态, offset: {offset}")
                res = await self.u.get_dynamics_new(offset)
                # logger.info(f"获取动态, offset: {offset}") # Redundant log
                if not res or "items" not in res:
                    logger.warning("API 未返回有效动态数据或已无更多动态")
                    break
            except Exception as ex:
                logger.error(f"获取动态时出错: {ex}")
                break

            all_exist_in_batch = True
            dynamics_to_insert = []

            for item in res.get("items", []):
                dynamic_id = item.get("id_str")
                if not dynamic_id:
                    logger.warning(f"跳过缺少 id_str 的动态项: {item}")
                    continue

                if dynamic_id not in existing_ids:
                    all_exist_in_batch = False
                    new_dynamics_count += 1

                    # 提取数据
                    author_module = item.get("modules", {}).get("module_author", {})
                    dynamic_module = item.get("modules", {}).get("module_dynamic", {})
                    stat_module = item.get("modules", {}).get("module_stat", {})
                    basic_info = item.get("basic", {})

                    pub_timestamp = author_module.get("pub_ts")
                    pub_datetime = (
                        datetime.datetime.fromtimestamp(pub_timestamp)
                        if pub_timestamp
                        else None
                    )

                    topic = get_nested_value(dynamic_module, ["topic", "name"])
                    context = None
                    if get_nested_value(
                        dynamic_module, ["major", "opus", "summary", "text"]
                    ):
                        context = get_nested_value(
                            dynamic_module, ["major", "opus", "summary", "text"], ""
                        ).replace("\n", " ")
                    elif get_nested_value(dynamic_module, ["desc", "text"]):
                        context = get_nested_value(
                            dynamic_module, ["desc", "text"], ""
                        ).replace("\n", " ").replace('\x00', '') # 移除空字节

                    share_num = get_nested_value(stat_module, ["forward", "count"], 0)
                    comment_num = get_nested_value(stat_module, ["comment", "count"], 0)
                    like_num = get_nested_value(stat_module, ["like", "count"], 0)

                    heat = round(U.dynamic_calculate_hotness(share_num, comment_num, like_num), 2)

                    dynamic_data = {
                        "uid": self.mid,
                        "name": author_module.get("name", self.char_zh),
                        "timestamp": pub_timestamp,
                        "datetime": pub_datetime,
                        "dynamic_content": context,
                        "url": f"https://www.bilibili.com/opus/{dynamic_id}",
                        "topic": topic,
                        "dynamic_id": str(dynamic_id),
                        "share_num": share_num,
                        "comment_num": comment_num,
                        "like_num": like_num,
                        "comment_id": basic_info.get("comment_id_str"),
                        "comment_type": basic_info.get("comment_type"),
                        "heat": heat
                    }
                    dynamics_to_insert.append(dynamic_data)
                    existing_ids.add(dynamic_id)

            # 3. 批量插入新动态
            if dynamics_to_insert:
                dynamics_ordered_keys = [
                    "uid", "name", "timestamp", "datetime", "dynamic_content", "url", "topic",
                    "dynamic_id", "share_num", "comment_num", "like_num", "comment_id",
                    "comment_type", "heat"
                ]
                dynamics_to_insert_tuples = [
                    tuple(d.get(key) for key in dynamics_ordered_keys) for d in dynamics_to_insert
                ]
                await self._execute_sql(
                    fatal_sql.insert_dynamics_table_sql,
                    executemany_params=dynamics_to_insert_tuples
                )

            # 4. 检查是否需要继续获取
            if all_exist_in_batch or not res.get("has_more"):
                logger.info(f"API 无更多新动态或当前批次动态均已存在于数据库")
                break

            offset = res.get("offset", "")
            if not offset:
                logger.warning("API 返回的 offset 为空，停止获取")
                break

            await asyncio.sleep(DYNAMICS_FETCH_DELAY)

        logger.info(f"动态更新完成，共新增 {new_dynamics_count} 条动态")

    async def fetch_all_video(self):
        """获取用户所有视频列表"""

        existing_bvid_sql = f"SELECT bvid FROM videos_table WHERE uid = $1"
        existing_results = await self._execute_sql(
            existing_bvid_sql, (self.mid,), fetch_all=True
        )
        existing_bvids = (
            set(row["bvid"] for row in existing_results)
            if existing_results
            else set()
        )
        logger.info(
            f"数据库中已存在 {len(existing_bvids)} 个 {self.char_zh} 的视频 BVID"
        )

        new_videos_count = 0
        page = 1
        api_video_count = 0
        total_api_videos = 0

        # 2. 获取视频列表
        while True:
            try:
                logger.info(f"获取视频列表, page: {page}")
                videos_page = await self.u.get_videos(
                    pn=page, ps=30
                )
                await asyncio.sleep(VIDEO_FETCH_DELAY)
                if (
                    not videos_page
                    or "list" not in videos_page
                    or "vlist" not in videos_page["list"]
                ):
                    logger.warning(
                        f"API 视频列表 page {page} 未返回有效数据或已无更多视频"
                    )
                    break
                if not videos_page["list"][
                    "vlist"
                ]:
                    logger.info(
                        f"API 视频列表 page {page} 返回空列表，已无更多视频"
                    )
                    break

                replies = videos_page["list"]["vlist"]
                if page == 1:
                    total_api_videos = videos_page.get("page", {}).get("count", 0)

                api_video_count += len(replies)
                logger.info(
                    f"API page {page}: 获取到 {len(replies)} 个视频 (累计 {api_video_count} / 总计 {total_api_videos})"
                )

            except Exception as ex:
                logger.error(f"获取视频列表 page {page} 时出错: {ex}")
                break

            videos_to_insert = []
            for v in replies:
                bvid = v.get("bvid")
                if not bvid:
                    logger.warning(f"跳过缺少 bvid 的视频项: {v}")
                    continue

                view = U.safe_int(v.get("play")) # Ensure view is an integer
                if view is None: # Check for None after safe_int conversion
                    logger.warning(f"跳过缺少或无效 view 的视频项: {v}")
                    continue

                try:
                    current_date_str = datetime.datetime.now().strftime("%Y-%m-%d")
                    prev_day = datetime.datetime.now() - datetime.timedelta(days=1)
                    prev_day_midnight = prev_day.replace(hour=0, minute=0, second=0, microsecond=0)


                    prev_day_stats = await self._execute_sql(
                        "SELECT view_num FROM video_day_data_table "
                        "WHERE uid = $1 AND bvid = $2 AND datetime = $3",
                        (self.mid, bvid, prev_day_midnight),
                        fetch_one=True
                    )

                    view_rise_num = view - U.safe_int(prev_day_stats["view_num"] if prev_day_stats else 0) if prev_day_stats else view

                    current_day_midnight = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                    day_data_dict = {
                        "uid": self.mid,
                        "name": self.char_zh,
                        "bvid": bvid,
                        "title": v.get("title"),
                        "create_time": v.get("created"),
                        "datetime": current_day_midnight, # datetime object
                        "view_num": view,
                        "view_rise_num": view_rise_num,
                    }
                    # Order for insert_video_day_data_table_sql:
                    # uid, name, bvid, title, create_time, datetime, view_num, view_rise_num
                    day_data_tuple = (
                        day_data_dict["uid"],
                        day_data_dict["name"],
                        day_data_dict["bvid"],
                        day_data_dict["title"],
                        day_data_dict["create_time"], # 修正为 create_time
                        day_data_dict["datetime"],
                        day_data_dict["view_num"],
                        day_data_dict["view_rise_num"],
                    )
                    await self._execute_sql(fatal_sql.insert_video_day_data_table_sql, day_data_tuple)
                    logger.debug(f"Inserted or updated daily view data for {bvid} on {current_date_str}")
                except Exception as day_view_err:
                    logger.error(f"Failed to insert or update daily view data for {bvid}: {day_view_err}")

                # 3. 检查是否为新视频 #TODO： rm this
                if bvid not in existing_bvids:
                    new_videos_count += 1
                    logger.info(
                        f"发现新视频: {bvid} ({v.get('title')})，准备获取详细信息..."
                    )

                    # 4. 获取详细信息 (包括统计、荣誉、AI总结等)
                    try:
                        all_res = await get_all_video_info(bvid)
                        await asyncio.sleep(ALL_VIDEO_INFO_DELAY)

                        # 获取 AI 总结
                        ai_summary_raw = await get_video_ai_conclusion(bvid, self.credential) # FIXME: now alwalys return empty
                        ai_summary = ai_summary_raw.get("model_result", {}).get("summary", "") if isinstance(ai_summary_raw, dict) else ""
                        await asyncio.sleep(VIDEO_AI_CONCLUSION_DELAY)

                        pub_timestamp = v.get("created")
                        pub_datetime = (
                            datetime.datetime.fromtimestamp(pub_timestamp)
                            if pub_timestamp
                            else None
                        )

                        play_num = U.safe_int(v.get("play", 0))
                        comment_num = U.safe_int(v.get("comment", 0))
                        # Ensure all_res is a dictionary before accessing its keys
                        like_num = U.safe_int(get_nested_value(all_res, ["stat", "like"], 0)) if isinstance(all_res, dict) else 0
                        coin = U.safe_int(get_nested_value(all_res, ["stat", "coin"], 0)) if isinstance(all_res, dict) else 0
                        favorite_num = U.safe_int(get_nested_value(all_res, ["stat", "favorite"], 0)) if isinstance(all_res, dict) else 0
                        honor_count = U.safe_int(get_nested_value(all_res, ["honor_count"], 0)) if isinstance(all_res, dict) else 0
                        share_num_val = U.safe_int(get_nested_value(all_res, ["stat", "share"], 0)) if isinstance(all_res, dict) else 0
                        danmuku_num_val = U.safe_int(get_nested_value(all_res, ["stat", "danmaku"], 0)) if isinstance(all_res, dict) else 0
                        honor_short_val = (
                            get_nested_value(all_res, ["honor_short"], "")[:-1]
                            if isinstance(all_res, dict) and get_nested_value(all_res, ["honor_short"]) and get_nested_value(all_res, ["honor_short"], "").endswith(",")
                            else (get_nested_value(all_res, ["honor_short"]) if isinstance(all_res, dict) else "")
                        )
                        honor_val = str(get_nested_value(all_res, ["honor"])) if isinstance(all_res, dict) else None


                        heat = round(U.video_calculate_hotness(play_num, comment_num, honor_count, like_num, coin, favorite_num), 2)

                        video_data = {
                            "uid": self.mid,
                            "name": self.char_zh,
                            "bvid": bvid,
                            "timestamp": pub_timestamp,
                            "datetime": pub_datetime,
                            "video_name": v.get("title", "").replace('\x00', ''), # 移除空字节
                            "description": v.get("description", "")
                            .replace("\n", " ")
                            .replace("\r", " ")
                            .replace('\x00', ''), # 移除空字节
                            "cover": v.get("pic"),
                            "play_num": play_num,
                            "comment_num": comment_num,
                            "like_num": like_num,
                            "coin": coin,
                            "favorite_num": favorite_num,
                            "share_num": share_num_val,
                            "danmuku_num": danmuku_num_val,
                            "aid": str(v.get("aid")) if v.get("aid") is not None else None, # Cast aid to string
                            "length": v.get("length"),
                            "honor_short": honor_short_val,
                            "honor_count": honor_count,
                            "honor": honor_val,
                            "video_ai_conclusion": ai_summary,
                            "heat": heat
                        }
                        videos_to_insert.append(video_data)
                        existing_bvids.add(bvid)

                    except Exception as detail_err:
                        logger.error(
                            f"获取视频 {bvid} 详细信息时出错: {detail_err}"
                        )
                        pub_timestamp = v.get("created")
                        pub_datetime = (
                            datetime.datetime.fromtimestamp(pub_timestamp)
                            if pub_timestamp
                            else None
                        )
                        basic_video_data = {
                            "uid": self.mid,
                            "name": self.char_zh,
                            "bvid": bvid,
                            "timestamp": pub_timestamp,
                            "datetime": pub_datetime,
                            "video_name": v.get("title"),
                            "description": v.get("description", "")
                            .replace("\n", " ")
                            .replace("\r", " "),
                            "cover": v.get("pic"),
                            "play_num": U.safe_int(v.get("play", 0)),
                            "comment_num": U.safe_int(v.get("comment", 0)),
                            "like_num": None,
                            "coin": None,
                            "favorite_num": None,
                            "share_num": None,
                            "danmuku_num": None,
                            "aid": str(v.get("aid")) if v.get("aid") is not None else None,
                            "length": v.get("length"),
                            "honor_short": None,
                            "honor_count": None,
                            "honor": None,
                            "summary": None,
                            "video_ai_conclusion": None,
                        }
                        videos_to_insert.append(basic_video_data)
                        existing_bvids.add(bvid)

            # 6. 批量插入新视频
            if videos_to_insert:
                try:
                    videos_ordered_keys = [
                        "uid", "name", "bvid", "timestamp", "datetime", "video_name", "description",
                        "cover", "play_num", "comment_num", "like_num", "coin", "favorite_num",
                        "share_num", "danmuku_num", "aid", "length", "honor_short", "honor_count",
                        "honor", "video_ai_conclusion", "heat"
                    ]
                    videos_to_insert_tuples = [
                        tuple(d.get(key) for key in videos_ordered_keys) for d in videos_to_insert
                    ]
                    await self._execute_sql(
                        fatal_sql.insert_videos_table_sql,
                        executemany_params=videos_to_insert_tuples
                    )
                    logger.info(
                        f"成功将 {len(videos_to_insert_tuples)} 个新视频信息插入数据库"
                    )
                except Exception as insert_err:
                    logger.error(f"插入新视频到数据库时出错: {insert_err}")

            # 7. 检查是否需要继续获取下一页
            if (
                api_video_count >= total_api_videos and total_api_videos > 0
            ):
                logger.info(f"已获取所有 {total_api_videos} 个视频")
                break
            page += 1
            await asyncio.sleep(VIDEO_FETCH_DELAY)

        logger.info(f"视频列表更新完成，共新增 {new_videos_count} 个视频")

    async def fetch_user_info(self):
        """获取用户信息"""
        try:
            logger.info(f"获取 {self.char_zh} 的信息")
            infos = await self.u.get_live_info()
            live_info = infos.get("live_room", {}) # Add default to avoid KeyError if live_room is missing
            api_room_id = live_info.get("roomid") # Store original room_id from API
            user_info_data_dict = {
                "uid": self.mid,
                "name": infos.get("name"),
                "face": infos.get("face"),
                "sign": infos.get("sign", "").replace('\x00', ''), # 移除空字节
                "birthday": infos.get("birthday"), # This is varchar(10) in DB, so string is fine
                "top_photo": infos.get("top_photo"),
                "room_id": str(api_room_id) if api_room_id is not None else None, # Use string version for SQL
                "live_url": live_info.get("url")
            }
            # Order for insert_user_info_table_sql:
            # uid, name, face, sign, birthday, top_photo, room_id, live_url
            user_info_data_tuple = (
                user_info_data_dict["uid"],
                user_info_data_dict["name"],
                user_info_data_dict["face"],
                user_info_data_dict["sign"],
                user_info_data_dict["birthday"],
                user_info_data_dict["top_photo"],
                user_info_data_dict["room_id"], # This is $7, now a string or None
                user_info_data_dict["live_url"],
            )
            await self._execute_sql(fatal_sql.insert_user_info_table_sql, user_info_data_tuple)
            logger.info(f"已将 {self.char_zh} 的信息存入/更新到数据库")
            return api_room_id # Return original room_id from API (not the stringified version)
        except Exception as ex:
            logger.error(f"获取或更新 {self.char_zh} 信息时出错: {ex}")
            return None

    async def fetch_all_dynamics_comments(self, interval_days=7):
        """
        获取用户所有动态的评论，并存入数据库，根据间隔天数决定是否更新。
        Fetches comments for all user dynamics and stores them in the database, updating based on the interval days.
        """
        logger.info(f"开始为用户 {self.char_zh} ({self.mid}) 获取所有动态评论...")

        comment_table_name = fatal_sql.get_dynamics_comment_table_name(self.mid)

        # 1. 获取dynamics_table中符合uid==self.mid的所有dynamic_id, comment_id和 comment_type

        get_dynamic_ids_sql = f"SELECT dynamic_id, dynamic_content, comment_id, comment_type FROM dynamics_table WHERE uid = $1"
        dynamic_ids_results = await self._execute_sql(
            get_dynamic_ids_sql, (self.mid,), fetch_all=True
        )
        dynamic_ids = [row["dynamic_id"] for row in dynamic_ids_results] if dynamic_ids_results else []
        logger.info(f"获取到 {len(dynamic_ids)} 条动态 ID 需要处理评论")

        await self._ensure_table_exists(fatal_sql.create_dynamics_comment_table_sql)

        current_date = datetime.datetime.now()
        update_threshold_date = current_date - datetime.timedelta(days=interval_days)

        for dynamic_id in dynamic_ids:
            logger.info(f"处理动态 {dynamic_id} 的评论...")

            # 2. 检查dynamics_comment_{self.mid}表中是否已有对应的oid动态评论
            check_comments_sql = f"SELECT MAX(timestamp) FROM {comment_table_name} WHERE oid = $1"
            latest_comment_result = await self._execute_sql(
                check_comments_sql, (dynamic_id,), fetch_one=True
            )
            latest_comment_time_val = latest_comment_result[0] if latest_comment_result and latest_comment_result[0] else None

            if latest_comment_time_val is not None: # Explicitly check for None
                # Comments exist, check if update is needed
                latest_comment_date = None
                if isinstance(latest_comment_time_val, (int, float)):
                    try:
                        latest_comment_date = datetime.datetime.fromtimestamp(latest_comment_time_val)
                    except ValueError as ve:
                        logger.error(f"Error converting timestamp {latest_comment_time_val} to datetime for dynamic {dynamic_id}: {ve}")
                        continue # Skip this dynamic if timestamp is invalid
                elif isinstance(latest_comment_time_val, datetime.datetime):
                    latest_comment_date = latest_comment_time_val
                else:
                    logger.warning(f"Unexpected type for latest_comment_time_val for dynamic {dynamic_id}: {type(latest_comment_time_val)}. Skipping update check for this dynamic.")
                    continue # Skip this dynamic if type is unexpected

                if latest_comment_date is not None and latest_comment_date < update_threshold_date:
                    logger.info(f"动态 {dynamic_id} 的评论数据超过 {interval_days} 天未更新，重新获取...")
                    # 从查询结果中获取对应的 comment_id 和 comment_type
                    comment_id = next((row["comment_id"] for row in dynamic_ids_results if row["dynamic_id"] == dynamic_id), None)
                    comment_type = next((row["comment_type"] for row in dynamic_ids_results if row["dynamic_id"] == dynamic_id), None)
                    dynamic_content = next((row["dynamic_content"] for row in dynamic_ids_results if row["dynamic_id"] == dynamic_id), None)
                    
                    if comment_id and comment_type:
                        # Fetch and insert new comments
                        comments_to_insert = await fetch_single_dynamic_comments(
                            self.mid, dynamic_id, dynamic_content, comment_id, comment_type, self.char_zh, self.credential
                        )
                    else:
                        logger.error(f"动态 {dynamic_id} 缺少 comment_id 或 comment_type，无法获取评论")
                        comments_to_insert = []
                    if comments_to_insert:
                        try:
                            dynamics_comment_ordered_keys = [
                                "up_uid", "up_name", "oid", "rpid", "from_dynamic", "mid", "uname",
                                "face", "timestamp", "datetime", "like_num", "comment", "rcount",
                                "parent_rpid", "is_sub_comment", "heat", "sentiment"
                            ]
                            comments_to_insert_tuples = [
                                tuple(d.get(key) for key in dynamics_comment_ordered_keys) for d in comments_to_insert
                            ]
                            await self._execute_sql(
                                fatal_sql.insert_dynamics_comment_table_sql(self.mid),
                                executemany_params=comments_to_insert_tuples
                            )
                            logger.info(f"成功将 {len(comments_to_insert_tuples)} 条动态评论插入数据库")
                        except Exception as insert_err:
                            logger.error(f"插入动态评论批次到数据库时出错: {insert_err}")
                else:
                    logger.info(f"动态 {dynamic_id} 的评论数据在 {interval_days} 天内已更新，跳过")
            else:
                # No comments exist, fetch all
                logger.info(f"动态 {dynamic_id} 尚无评论数据，获取所有评论...")
                # 从查询结果中获取对应的 comment_id 和 comment_type
                comment_id = next((row["comment_id"] for row in dynamic_ids_results if row["dynamic_id"] == dynamic_id), None)
                comment_type = next((row["comment_type"] for row in dynamic_ids_results if row["dynamic_id"] == dynamic_id), None)
                dynamic_content = next((row["dynamic_content"] for row in dynamic_ids_results if row["dynamic_id"] == dynamic_id), None)

                if comment_id and comment_type:
                    comments_to_insert = await fetch_single_dynamic_comments(
                        self.mid, dynamic_id, dynamic_content, comment_id, comment_type, self.char_zh, self.credential
                    )
                else:
                    logger.error(f"动态 {dynamic_id} 缺少 comment_id 或 comment_type，无法获取评论")
                    comments_to_insert = []
                if comments_to_insert:
                    try:
                        dynamics_comment_ordered_keys = [
                            "up_uid", "up_name", "oid", "rpid", "from_dynamic", "mid", "uname",
                            "face", "timestamp", "datetime", "like_num", "comment", "rcount",
                            "parent_rpid", "is_sub_comment", "heat", "sentiment"
                        ]
                        comments_to_insert_tuples = [
                            tuple(d.get(key) for key in dynamics_comment_ordered_keys) for d in comments_to_insert
                        ]
                        await self._execute_sql(
                            fatal_sql.insert_dynamics_comment_table_sql(self.mid),
                            executemany_params=comments_to_insert_tuples
                        )
                        logger.info(f"成功将 {len(comments_to_insert_tuples)} 条动态评论插入数据库")
                    except Exception as insert_err:
                        logger.error(f"插入动态评论批次到数据库时出错: {insert_err}")
                else:
                    logger.info(f"动态 {dynamic_id}: 没有新的动态评论需要插入数据库")

            await asyncio.sleep(DYNAMICS_FETCH_DELAY)

        logger.info(f"所有动态的评论处理完成。")

    async def fetch_all_videos_comments(self, interval_days=7):
        """
        获取用户所有视频的评论，并存入数据库，根据间隔天数决定是否更新。
        Fetches comments for all user videos and stores them in the database, updating based on the interval days.
        """
        logger.info(f"开始为用户 {self.char_zh} ({self.mid}) 获取所有视频评论...")

        comment_table_name = fatal_sql.get_video_comment_table_name(self.mid)

        # 1. 获取videos_table中符合uid==self.mid的所有bvid
        get_video_bvid_sql = f"SELECT bvid, video_name  FROM videos_table WHERE uid = $1"
        video_bvid_results = await self._execute_sql(
            get_video_bvid_sql, (self.mid,), fetch_all=True
        )
        video_bvids = [row["bvid"] for row in video_bvid_results] if video_bvid_results else []
        logger.info(f"获取到 {len(video_bvids)} 个视频 BVID 需要处理评论")

        # Ensure the comment table exists
        await self._ensure_table_exists(fatal_sql.create_video_comment_table_sql)

        current_date = datetime.datetime.now()
        update_threshold_date = current_date - datetime.timedelta(days=interval_days)

        for bvid in video_bvids:
            logger.info(f"处理视频 {bvid} 的评论...")

            # 2. 检查video_comment_table_{self.mid}表中是否已有对应的oid视频评论
            check_comments_sql = f"SELECT MAX(timestamp) FROM {comment_table_name} WHERE oid = $1"
            latest_comment_result = await self._execute_sql(
                check_comments_sql, (bvid,), fetch_one=True
            )
            latest_comment_time = latest_comment_result[0] if latest_comment_result and latest_comment_result[0] else None

            if latest_comment_time and isinstance(latest_comment_time, (int, float)): # Ensure datetime
                latest_comment_time = datetime.datetime.fromtimestamp(latest_comment_time)

            video_title = next((row["video_name"] for row in video_bvid_results if row["bvid"] == bvid), None)

            if latest_comment_time:
                # Comments exist, check if update is needed
                latest_comment_date = latest_comment_time # Assuming comment_time is stored as datetime
                if latest_comment_date < update_threshold_date:
                    logger.info(f"视频 {bvid} 的评论数据超过 {interval_days} 天未更新，重新获取...")

                    comments_to_insert = await fetch_single_video_comments(
                        self.mid, bvid, video_title, self.char_zh, self.credential
                    )
                    if comments_to_insert:
                        try:
                            video_comment_ordered_keys = [
                                "up_uid", "up_name", "oid", "bvid", "from_video_title", "rpid",
                                "mid", "uname", "face", "timestamp", "datetime", "like_num",
                                "comment", "rcount", "parent_rpid", "is_sub_comment", "heat", "sentiment"
                            ]
                            comments_to_insert_tuples = [
                                tuple(d.get(key) for key in video_comment_ordered_keys) for d in comments_to_insert
                            ]
                            await self._execute_sql(
                                fatal_sql.insert_video_comment_table_sql(self.mid),
                                executemany_params=comments_to_insert_tuples
                            )
                            logger.info(f"成功将 {len(comments_to_insert_tuples)} 条视频评论插入数据库")
                        except Exception as insert_err:
                            logger.error(f"插入视频评论批次到数据库时出错: {insert_err}")
                    else:
                        logger.info(f"视频 {bvid}: 没有新的视频评论需要插入数据库")
                else:
                    logger.info(f"视频 {bvid} 的评论数据在 {interval_days} 天内已更新，跳过")
            else:
                # No comments exist, fetch all
                logger.info(f"视频 {bvid} 尚无评论数据，获取所有评论...")
                comments_to_insert =  await fetch_single_video_comments(
                        self.mid, bvid, video_title, self.char_zh, self.credential
                    )
                if comments_to_insert:
                    try:
                        video_comment_ordered_keys = [
                            "up_uid", "up_name", "oid", "bvid", "from_video_title", "rpid",
                            "mid", "uname", "face", "timestamp", "datetime", "like_num",
                            "comment", "rcount", "parent_rpid", "is_sub_comment", "heat", "sentiment"
                        ]
                        comments_to_insert_tuples = [
                            tuple(d.get(key) for key in video_comment_ordered_keys) for d in comments_to_insert
                        ]
                        await self._execute_sql(
                            fatal_sql.insert_video_comment_table_sql(self.mid),
                            executemany_params=comments_to_insert_tuples
                        )
                        logger.info(f"成功将 {len(comments_to_insert_tuples)} 条视频评论插入数据库")
                    except Exception as insert_err:
                        logger.error(f"插入视频评论批次到数据库时出错: {insert_err}")
                else:
                    logger.info(f"视频 {bvid}: 没有新的视频评论需要插入数据库")
            
            await asyncio.sleep(VIDEO_FETCH_DELAY)

        logger.info(f"所有视频的评论处理完成。")

    async def fetch_tieba_whole(
        self, extra_tieba_name=""
    ):
        """从数据库获取或更新指定贴吧的完整数据（主题帖、帖子、评论）"""
        if extra_tieba_name:
            target_tieba = extra_tieba_name
        else:
            from backend.utils.utils import get_vup_tieba_name_by_short_name
            target_tieba = get_vup_tieba_name_by_short_name(self.char_zh)

        if not target_tieba:
            logger.error(
                f"未找到角色 {self.char_zh} 或指定贴吧 {extra_tieba_name} 的配置"
            )
            return []

        # --- Dynamic Table Handling ---
        await self._ensure_table_exists(fatal_sql.get_create_tieba_whole_table_sql)
        table_name = fatal_sql.get_tieba_whole_table_name(self.mid)
        insert_sql = fatal_sql.get_insert_tieba_whole_table_sql(self.mid)
        # --- End Dynamic Table Handling ---

        # 1. 确保主题帖列表是最新的
        logger.info(f"开始更新 {target_tieba} 的主题帖列表...")
        # 调用已重构的 get_tieba_threads 获取最新主题帖列表（它会处理数据库更新）
        # 注意：这里需要获取原始的 thread 对象列表，而不仅仅是格式化的数据
        # 可能需要修改 get_tieba_threads 或 tieba_crawler_thread
        # 暂时假设 tieba_crawler_thread 返回所需对象
        try:
            thread_list_api = await tieba_crawler_thread(target_tieba)
            logger.info(
                f"获取到 {len(thread_list_api)} 个主题帖用于检查更新"
            )
        except Exception as thread_err:
            logger.error(f"获取 {target_tieba} 主题帖列表时出错: {thread_err}")
            thread_list_api = []  # 出错则无法继续

        # 2. 获取数据库中已存在的 pid (包括主题帖 pid=0, 帖子 pid, 评论 pid) for the specific up_uid table
        existing_pid_sql = f"SELECT pid FROM {table_name} WHERE up_uid = $1 AND fname = $2" # Added up_uid condition
        existing_results = await self._execute_sql(
            existing_pid_sql, (self.mid, target_tieba), fetch_all=True # Pass self.mid
        )
        existing_pids = (
            set(str(row["pid"]) for row in existing_results)
            if existing_results
            else set()
        )
        logger.info(f"数据库中已存在 {len(existing_pids)} 条 {target_tieba} 的 PID")

        new_items_count = 0
        items_to_insert = []

        # 3. 遍历主题帖，获取帖子和评论
        async with tb.Client(BDUSS) as client:
            for thread in thread_list_api:
                thread_pid_str = "0"  # 主题帖的 pid 视为 0
                thread_tid_str = str(thread.tid)

                # 检查主题帖本身是否已在 whole 表中 (用 tid 和 pid=0 联合判断可能更准确，但 pid=0 可能冲突)
                # 简单起见，如果 thread_tid_str 对应的任何 pid 在库里，就认为主题帖信息可能已存在
                # 更稳妥的方式是单独检查 level_num=0 的记录
                # 这里简化处理：如果主题帖的 tid 在 threads 表中已存在，则认为主题帖信息已处理过
                # (依赖 get_tieba_threads 的更新逻辑)

                # 检查并添加主题帖信息到插入列表 (如果需要且不存在)
                # 注意：tieba_whole_table 可能不存 level=0 的数据，取决于设计
                # 假设我们需要存储 level=0 的数据
                # if thread_pid_str not in existing_pids: # 检查 pid=0 是否存在可能不准确
                # 检查 level=0 的记录是否存在 for the specific up_uid table
                check_thread_sql = f"SELECT 1 FROM {table_name} WHERE up_uid = $1 AND fname = $2 AND tid = $3 AND level_num = 0 LIMIT 1" # Added up_uid condition
                thread_exists = await self._execute_sql(
                    check_thread_sql, (self.mid, target_tieba, str(thread.tid)), fetch_one=True # Pass self.mid
                )

                if not thread_exists:
                    new_items_count += 1
                    create_time_dt = (
                        datetime.datetime.fromtimestamp(thread.create_time)
                        if thread.create_time
                        else None
                    )
                    last_time_dt = (
                        datetime.datetime.fromtimestamp(thread.last_time)
                        if thread.last_time
                        else None
                    )
                    thread_info_whole = {
                        "fid": thread.fid,
                        "fname": thread.fname,
                        "tid": thread.tid,
                        "user_name": thread.user.nick_name_new,
                        "create_time": create_time_dt,
                        "up_uid": self.mid, # Add up_uid
                        "fid": thread.fid,
                        "fname": thread.fname,
                        "tid": thread.tid,
                        "user_name": thread.user.nick_name_new,
                        "create_time": create_time_dt,
                        "last_time": last_time_dt,
                        "title": thread.title,
                        "text": thread.text.replace('\x00', '') if thread.text is not None else None, # 移除空字节，并确保 None 安全
                        "img": (
                            thread.contents.imgs[0].src
                            if hasattr(thread.contents, 'imgs') and thread.contents.imgs and len(thread.contents.imgs) > 0
                            else None
                        ),
                        "view_num": thread.view_num,
                        "reply_num": thread.reply_num,
                        "agree": thread.agree,
                        "disagree": thread.disagree,
                        "level_num": 0,
                        "pid": 0,
                        "floor": 0,  # 主题帖信息
                    }
                    items_to_insert.append(thread_info_whole)
                    existing_pids.add(thread_pid_str)  # 标记 pid=0 已处理
                    if len(items_to_insert) >= TIEBA_INSERT_POOL_SIZE:
                        tieba_whole_ordered_keys = [
                            "up_uid", "fid", "fname", "tid", "pid", "user_name", "create_time", 
                            "last_time", "title", "text", "img", "view_num", "reply_num", 
                            "agree", "disagree", "level_num", "floor"
                        ]
                        await self._batch_insert_data(table_name, insert_sql, items_to_insert, tieba_whole_ordered_keys)
                        items_to_insert = []

                # 4. 获取并处理帖子和评论
                # 确保 thread.title 不为 None
                thread_title_for_log = thread.title if thread.title is not None else "无标题"
                logger.info(
                    f"检查主题帖 {thread.tid} ({thread_title_for_log}) 的帖子和评论..."
                )
                current_page = 1
                while True:  # 循环获取分页
                    try:
                        logger.debug(
                            f"获取帖子: tid={thread.tid}, page={current_page}"
                        )
                        posts = await client.get_posts(
                            thread.tid, pn=current_page, with_comments=True
                        )
                        await asyncio.sleep(TIEBA_WHOLE_DELAY)

                        if not posts or len(posts) == 0:
                            logger.debug(
                                f"主题帖 {thread.tid} 第 {current_page} 页无帖子"
                            )
                            break  # 当前页没有帖子，结束该主题帖的处理

                        for post in posts:
                            post_pid_str = str(post.pid)
                            if post_pid_str not in existing_pids:
                                new_items_count += 1
                                post_create_time_dt = (
                                    datetime.datetime.fromtimestamp(
                                        post.create_time
                                    )
                                    if post.create_time
                                    else None
                                )
                                post_info_whole = {
                                    "up_uid": self.mid,
                                    "fid": post.fid,
                                    "fname": post.fname,
                                    "tid": post.tid,
                                    "user_name": post.user.nick_name_new,
                                    "create_time": post_create_time_dt,
                                    "last_time": post_create_time_dt,
                                    "title": thread.title,  # 使用主题帖标题
                                    "text": post.text.replace('\x00', '') if post.text is not None else None, # 移除空字节，并确保 None 安全
                                    "img": (
                                        post.contents.imgs[0].src
                                        if hasattr(post.contents, 'imgs') and post.contents.imgs and len(post.contents.imgs) > 0
                                        else None
                                    ),
                                    "view_num": thread.view_num,  # 使用主题帖观看数
                                    "reply_num": post.reply_num,  # 帖子自己的回复数 (楼中楼)
                                    "agree": post.agree,
                                    "disagree": post.disagree,
                                    "level_num": 1,
                                    "pid": post.pid,
                                    "floor": post.floor,
                                }
                                items_to_insert.append(post_info_whole)
                                existing_pids.add(post_pid_str)
                                if len(items_to_insert) >= TIEBA_INSERT_POOL_SIZE:
                                    tieba_whole_ordered_keys = [
                                        "up_uid", "fid", "fname", "tid", "pid", "user_name", "create_time", 
                                        "last_time", "title", "text", "img", "view_num", "reply_num", 
                                        "agree", "disagree", "level_num", "floor"
                                    ]
                                    await self._batch_insert_data(table_name, insert_sql, items_to_insert, tieba_whole_ordered_keys)
                                    items_to_insert = []

                            # 处理楼中楼评论
                            if post.comments:
                                for comment in post.comments:
                                    comment_pid_str = str(comment.pid)
                                    if comment_pid_str not in existing_pids:
                                        new_items_count += 1
                                        comment_create_time_dt = (
                                            datetime.datetime.fromtimestamp(
                                                comment.create_time
                                            )
                                            if comment.create_time
                                            else None
                                        )
                                        comment_info_whole = {
                                            "up_uid": self.mid, # Add up_uid
                                            "fid": comment.fid,
                                            "fname": comment.fname,
                                            "tid": comment.tid,
                                            "user_name": comment.user.nick_name_new,
                                            "create_time": comment_create_time_dt,
                                            "last_time": comment_create_time_dt,
                                            "title": thread.title,  # 使用主题帖标题
                                            "text": comment.text.replace('\x00', '') if comment.text is not None else None, # 移除空字节，并确保 None 安全
                                            "img": (
                                                comment.contents.imgs[0].src
                                                if hasattr(comment.contents, 'imgs') and comment.contents.imgs and len(comment.contents.imgs) > 0
                                                else None
                                            ),
                                            "view_num": thread.view_num,  # 使用主题帖观看数
                                            "reply_num": 0,
                                            "agree": comment.agree,
                                            "disagree": comment.disagree,
                                            "level_num": 2,
                                            "pid": comment.pid,
                                            "floor": comment.floor
                                        }
                                        items_to_insert.append(comment_info_whole)
                                        existing_pids.add(comment_pid_str)
                                        if len(items_to_insert) >= TIEBA_INSERT_POOL_SIZE:
                                            tieba_whole_ordered_keys = [
                                                "up_uid", "fid", "fname", "tid", "pid", "user_name", "create_time", 
                                                "last_time", "title", "text", "img", "view_num", "reply_num", 
                                                "agree", "disagree", "level_num", "floor"
                                            ]
                                            await self._batch_insert_data(table_name, insert_sql, items_to_insert, tieba_whole_ordered_keys)
                                            items_to_insert = []

                        # 检查是否有下一页
                        if not posts.has_more:
                            logger.debug(f"主题帖 {thread.tid} 已无更多帖子")
                            break  # 没有更多页了

                        current_page += 1  # 前往下一页

                    except Exception as post_err:
                        logger.error(
                            f"获取主题帖 {thread.tid} 第 {current_page} 页帖子时出错: {post_err}"
                        )
                        break  # 获取出错，停止该主题帖的处理

        # 5. 批量插入剩余的新数据
        if items_to_insert:
            tieba_whole_ordered_keys = [
                "up_uid", "fid", "fname", "tid", "pid", "user_name", "create_time", 
                "last_time", "title", "text", "img", "view_num", "reply_num", 
                "agree", "disagree", "level_num", "floor"
            ]
            await self._batch_insert_data(table_name, insert_sql, items_to_insert, tieba_whole_ordered_keys)

        logger.info(
            f"贴吧 {target_tieba} 完整数据更新完成，共处理/新增 {new_items_count} 条项目"
        )

    async def fetch_tieba_threads(
        self, extra_tieba_name=""
    ):
        """从数据库获取或更新指定贴吧的主题帖列表"""
        if extra_tieba_name:
            target_tieba = extra_tieba_name
        else:
            from backend.utils.utils import get_vup_tieba_name_by_short_name
            target_tieba = get_vup_tieba_name_by_short_name(self.char_zh)

        if not target_tieba:
            logger.error(
                f"未找到角色 {self.char_zh} 或指定贴吧 {extra_tieba_name} 的配置"
            )
            return []

        # --- Dynamic Table Handling ---
        table_name = "tieba_threads_table"
        insert_sql = fatal_sql.insert_tieba_threads_table_sql
        # --- End Dynamic Table Handling ---

        # 1. 获取数据库中已存在的 tid
        existing_tid_sql = f"SELECT tid FROM tieba_threads_table WHERE up_uid = $1 AND fname = $2"
        existing_results = await self._execute_sql(
            existing_tid_sql, (self.mid, target_tieba), fetch_all=True
        )
        existing_tids = (
            set(str(row["tid"]) for row in existing_results)
            if existing_results
            else set()
        )  # tid 转为字符串比较
        logger.info(
            f"数据库中已存在 {len(existing_tids)} 条 {target_tieba} 的主题帖 TID"
        )

        new_threads_count = 0
        threads_to_insert = []
        try:
            # 2. 获取主题帖列表
            logger.info(f"获取 {target_tieba} 的主题帖列表...")
            thread_list_api = await tieba_crawler_thread(target_tieba)
            logger.info(f"获取到 {len(thread_list_api)} 条主题帖")

            await asyncio.sleep(TIEBA_THREAD_DELAY)

            # 3. 检查新主题帖
            for thread in thread_list_api:
                tid_str = str(thread.tid)
                if tid_str not in existing_tids:
                    new_threads_count += 1
                    create_time_dt = (
                        datetime.datetime.fromtimestamp(thread.create_time)
                        if thread.create_time
                        else None
                    )
                    last_time_dt = (
                        datetime.datetime.fromtimestamp(thread.last_time)
                        if thread.last_time
                        else None
                    )

                    thread_data = {
                        "up_uid": self.mid, # Add up_uid
                        "fid": thread.fid,
                        "fname": thread.fname,
                        "tid": thread.tid,
                        "user_name": thread.user.nick_name_new,
                        "create_time": create_time_dt,
                        "last_time": last_time_dt,
                        "title": thread.title,
                        "text": thread.text.replace('\x00', '') if thread.text else None, # 移除空字节
                        "img": (
                            thread.contents.imgs[0].src
                            if hasattr(thread.contents, 'imgs') and thread.contents.imgs
                            else None
                        ),
                        "view_num": thread.view_num,
                        "reply_num": thread.reply_num,
                        "share_num": thread.share_num,
                        "agree": thread.agree,
                        "disagree": thread.disagree,
                    }
                    threads_to_insert.append(thread_data)
                    existing_tids.add(tid_str)  # 添加到集合避免重复
                    if len(threads_to_insert) >= TIEBA_INSERT_POOL_SIZE:
                        tieba_threads_ordered_keys = [
                            "up_uid", "fid", "fname", "tid", "user_name", "create_time", "last_time", 
                            "title", "text", "img", "view_num", "reply_num", "share_num", 
                            "agree", "disagree"
                        ]
                        await self._batch_insert_data(table_name, insert_sql, threads_to_insert, tieba_threads_ordered_keys)
                        threads_to_insert = []

        except Exception as e:
            logger.error(f"获取或处理 {target_tieba} 主题帖列表时出错: {e}")

        # 4. 批量插入剩余的新主题帖
        if threads_to_insert:
            tieba_threads_ordered_keys = [
                "up_uid", "fid", "fname", "tid", "user_name", "create_time", "last_time", 
                "title", "text", "img", "view_num", "reply_num", "share_num", 
                "agree", "disagree"
            ]
            await self._batch_insert_data(table_name, insert_sql, threads_to_insert, tieba_threads_ordered_keys)

        logger.info(f"主题帖更新完成，共新增 {new_threads_count} 条")

    async def gen_recent_relationships(self, recent_days: int = 100):
        logger.info(f"为 {self.char_zh} ({self.mid}) 生成最近 {recent_days} 天的关系数据")
        rs = RelationshipSummarize(self.char_zh, AI_GEN_MODEL)
        res = await rs.run(recent_days)

        current_dt = datetime.datetime.now()
        current_ts = int(current_dt.timestamp())
        insert_data_dict = {
            "uid": self.mid,
            "name": self.char_zh,
            "cur_datetime": current_dt.replace(minute=0, second=0, microsecond=0),
            "cur_timestamp": current_ts,
            "recent": recent_days,
            "relationships": json.dumps(res, ensure_ascii=False),
            # tieba_summaries, etc., are NULL in the SQL VALUES clause
        }
        # For VALUES ($1, $2, $3, $4, $5, $6, NULL, NULL, NULL)
        # Order for insert_or_update_relationships_sql
        insert_data_tuple = (
            insert_data_dict["uid"],
            insert_data_dict["name"],
            insert_data_dict["cur_datetime"],
            insert_data_dict["cur_timestamp"],
            insert_data_dict["recent"],
            insert_data_dict["relationships"],
        )
        await self._execute_sql(fatal_sql.insert_or_update_relationships_sql, insert_data_tuple)
        logger.info(f"已将 {self.char_zh} 的最近关系数据存入 AI_GEN_TABLE")

    async def _process_sentiment_for_table(self, table_name, id_column, text_column, batch_size=50):
        """Helper function to process sentiment for a given table."""
        logger.info(f"开始处理表 {table_name} 的情感分析...")

        # 1. 查询需要处理的数据
        query_sql = f"""
            SELECT {id_column}, {text_column}
            FROM {table_name}
            WHERE sentiment IS NULL AND {text_column} IS NOT NULL AND {text_column} != ''
        """
        # 注意：这里不直接使用 self._execute_sql 获取所有数据，避免内存问题
        # 而是使用 cursor 逐批获取或分页获取 (如果数据量巨大)
        # 为简化起见，先尝试获取所有，如果遇到性能问题再优化

        rows_to_process = await self._execute_sql(query_sql, fetch_all=True)

        if not rows_to_process:
            logger.info(f"表 {table_name} 中没有需要处理情感分析的数据。")
            return

        logger.info(f"表 {table_name} 中找到 {len(rows_to_process)} 条需要处理的数据。")

        update_sql = f"UPDATE {table_name} SET sentiment = $1 WHERE {id_column} = $2"
        update_data_batch = []
        processed_count = 0

        # 2. 分批处理
        for i in range(0, len(rows_to_process), batch_size):
            batch = rows_to_process[i:i + batch_size]
            texts = [row[text_column] for row in batch]
            ids = [row[id_column] for row in batch]

            if not texts:
                continue

            try:
                # 3. 调用情感分析函数
                # senta_sentences_sentiment_analysis 返回 [(text, label, score), ...]
                sentiment_results = senta_sentences_sentiment_analysis(texts)
                logger.debug(f"处理表 {table_name} 批次 {i // batch_size + 1}: 获取到 {len(sentiment_results)} 个情感分析结果。")

                # 准备更新数据
                for idx, result in enumerate(sentiment_results):
                    # result 结构: (原始文本, 情感标签, 情感得分)
                    # 将得分与原始 ID 对应起来
                    original_id = ids[idx]
                    sentiment_score = result[2] # 获取情感得分
                    update_data_batch.append((sentiment_score, original_id))

                # 4. 批量更新数据库
                if update_data_batch:
                    try:
                        await self._execute_sql(update_sql, executemany_params=update_data_batch)
                        processed_count += len(update_data_batch)
                        logger.info(f"成功更新表 {table_name} 中 {len(update_data_batch)} 条记录的情感分数 (累计 {processed_count})。")
                        update_data_batch = [] # 清空批次
                    except Exception as update_err:
                        logger.error(f"更新表 {table_name} 情感分数时出错: {update_err}")
                        # 清空批次以避免重复尝试失败的数据
                        update_data_batch = []
                        # 可以考虑在这里添加更复杂的重试逻辑或跳过批次

            except Exception as analysis_err:
                logger.error(f"调用情感分析函数处理表 {table_name} 批次时出错: {analysis_err}")
                # 跳过当前批次

        logger.info(f"表 {table_name} 的情感分析处理完成，共更新 {processed_count} 条记录。")

    async def gen_comment_sensiment(self):
        """
        从数据库读取视频评论、动态评论和贴吧内容，计算情感值，并将结果存回数据库。
        """
        logger.info(f"开始为用户 {self.char_zh} ({self.mid}) 生成评论/内容的情感分析...")

        # --- 处理视频评论 ---
        video_comment_table = fatal_sql.get_video_comment_table_name(self.mid)
        await self._ensure_table_exists(fatal_sql.create_video_comment_table_sql)
        await self._process_sentiment_for_table(video_comment_table, "rpid", "comment")

        # --- 处理动态评论 ---
        dynamic_comment_table = fatal_sql.get_dynamics_comment_table_name(self.mid)
        await self._ensure_table_exists(fatal_sql.create_dynamics_comment_table_sql)
        await self._process_sentiment_for_table(dynamic_comment_table, "rpid", "comment")

        # --- 处理贴吧内容 ---
        tieba_whole_table = fatal_sql.get_tieba_whole_table_name(self.mid)
        await self._ensure_table_exists(fatal_sql.get_create_tieba_whole_table_sql)
        await self._process_sentiment_for_table(tieba_whole_table, "pid", "text")

        logger.info(f"用户 {self.char_zh} ({self.mid}) 的所有评论/内容情感分析处理完成。")

    async def gen_comment_topics(self, recent_days: int = 30):
        cs = CommentSummarize(self.char_zh, AI_GEN_MODEL)
        res = await cs.run(recent_days)
        tieba_summaries = json.dumps(res[0], ensure_ascii=False)
        
        tieba_summaries_total = json.dumps(res[1], ensure_ascii=False)
        # --- Store result in AI_GEN_TABLE ---
        current_dt = datetime.datetime.now()
        current_ts = int(current_dt.timestamp())
        insert_data_dict = {
            "uid": self.mid,
            "name": self.char_zh,
            "cur_datetime": current_dt.replace(minute=0, second=0, microsecond=0),
            "cur_timestamp": current_ts,
            "recent": recent_days,
            "relationships": None,
            "tieba_summaries": tieba_summaries,
            "tieba_summaries_total": tieba_summaries_total,
            "rise_reason": None,
        }
        # Order for insert_ai_gen_table_sql:
        # uid, name, cur_datetime, cur_timestamp, recent, relationships,
        # tieba_summaries, tieba_summaries_total, rise_reason
        insert_data_tuple = (
            insert_data_dict["uid"],
            insert_data_dict["name"],
            insert_data_dict["cur_datetime"],
            insert_data_dict["cur_timestamp"],
            insert_data_dict["recent"],
            insert_data_dict["relationships"],
            insert_data_dict["tieba_summaries"],
            insert_data_dict["tieba_summaries_total"],
            insert_data_dict["rise_reason"],
        )
        await self._execute_sql(fatal_sql.insert_ai_gen_table_sql, insert_data_tuple)
        logger.info(f"已将 {self.char_zh} 的评论主题总结存入 AI_GEN_TABLE")
        # --- End storing result ---
        return res

    async def fetch_dahanghai_list(self):
        current_time_str = datetime.datetime.now().strftime("%Y-%m-%d")
        current_dt_obj = datetime.datetime.now()

        if not self.liveroom:
            self.liveroom = live.LiveRoom(
                int(self.liveid), credential=self.credential
            )

        # Check if data for the current up_uid and date already exists in the database
        current_date_val = current_dt_obj.date()
        check_sql = """
            SELECT 1 FROM dahanghai_list_table
            WHERE up_uid = $1 AND DATE(datetime) = $2
            LIMIT 1;
        """
        existing_db_entry_for_today = None
        error_during_check = False
        try:
            existing_db_entry_for_today = await self._execute_sql(check_sql, (self.mid, current_date_val), fetch_one=True)
        except Exception as e:
            logger.error(f"Error checking existing Dahanghai data in DB for {self.char_zh} ({self.mid}): {e}")
            error_during_check = True

        if error_during_check:
            # An error occurred during the database check, so we return.
            return

        if existing_db_entry_for_today:
            logger.info(
                f"Dahanghai list Data for {self.char_zh} ({self.mid}) on {current_time_str} (date: {current_date_val}) already exists in the database. Skipping."
            )
            return

        try:
            all_dahanghai_entries_for_db = []
            
            # Fetch first page and top3
            dahanghai_reply = await self.liveroom.get_dahanghai()
            info_block = dahanghai_reply.get("info", {})
            total_pages = info_block.get("page", 0)
            total_num_fans = info_block.get("num", 0)

            top3_list = dahanghai_reply.get("top3", [])
            page1_list = dahanghai_reply.get("list", [])
            
            processed_fan_uids_today = set()

            # Process top3
            for fan_data in top3_list:
                fan_uid = fan_data.get("uid")
                if not fan_uid or fan_uid in processed_fan_uids_today:
                    continue
                processed_fan_uids_today.add(fan_uid)
                
                db_entry = {
                    "up_uid": self.mid,
                    "up_name": self.char_zh,
                    "time": current_dt_obj.date(),
                    "datetime": current_dt_obj,
                    "num": total_num_fans,
                    "page": 1,
                    "uid": fan_uid,
                    "ruid": fan_data.get("ruid"),
                    "rank": fan_data.get("rank"),
                    "username": fan_data.get("username"),
                    "face": fan_data.get("face"),
                    "guard_level": fan_data.get("guard_level"),
                    "guard_sub_level": fan_data.get("guard_sub_level", 0),
                    "if_top3": True
                }
                all_dahanghai_entries_for_db.append(db_entry)

            # Process page 1 list (excluding those already in top3)
            for fan_data in page1_list:
                fan_uid = fan_data.get("uid")
                if not fan_uid or fan_uid in processed_fan_uids_today:
                    continue
                processed_fan_uids_today.add(fan_uid)

                db_entry = {
                    "up_uid": self.mid,
                    "up_name": self.char_zh,
                    "time": current_dt_obj.date(),
                    "datetime": current_dt_obj,
                    "num": total_num_fans,
                    "page": 1,
                    "uid": fan_uid,
                    "ruid": fan_data.get("ruid"),
                    "rank": fan_data.get("rank"),
                    "username": fan_data.get("username"),
                    "face": fan_data.get("face"),
                    "guard_level": fan_data.get("guard_level"),
                    "guard_sub_level": fan_data.get("guard_sub_level", 0),
                    "if_top3": False 
                }
                all_dahanghai_entries_for_db.append(db_entry)

            # Fetch subsequent pages
            for i in range(2, total_pages + 1):
                logger.info(f"Fetching Dahanghai list page {i} / {total_pages} for {self.char_zh} ({self.mid})...")
                dahanghai_page_reply = await self.liveroom.get_dahanghai(page=i)
                current_page_list = dahanghai_page_reply.get("list", [])

                for fan_data in current_page_list:
                    fan_uid = fan_data.get("uid")
                    if not fan_uid or fan_uid in processed_fan_uids_today:
                        continue
                    processed_fan_uids_today.add(fan_uid)

                    db_entry = {
                        "up_uid": self.mid,
                        "up_name": self.char_zh,
                        "time": current_dt_obj.date(),
                        "datetime": current_dt_obj,
                        "num": total_num_fans,
                        "page": i,
                        "uid": fan_uid,
                        "ruid": fan_data.get("ruid"),
                        "rank": fan_data.get("rank"),
                        "username": fan_data.get("username"),
                        "face": fan_data.get("face"),
                        "guard_level": fan_data.get("guard_level"),
                        "guard_sub_level": fan_data.get("guard_sub_level", 0),
                        "if_top3": False
                    }
                    all_dahanghai_entries_for_db.append(db_entry)
                await asyncio.sleep(DAHANGHAI_LIST_FETCH_LIST_DELAY)

            logger.info(
                f"Fetched Dahanghai list for {self.char_zh} ({self.mid}): {len(all_dahanghai_entries_for_db)} entries from API (expected total: {total_num_fans})"
            )

            # Insert into database
            if all_dahanghai_entries_for_db:
                try:
                    dahanghai_ordered_keys = [
                        "up_uid", "up_name", "time", "datetime", "num", "page", "uid", "ruid",
                        "rank", "username", "face", "guard_level", "guard_sub_level", "if_top3"
                    ]
                    dahanghai_entries_tuples = [
                        tuple(d.get(key) for key in dahanghai_ordered_keys) for d in all_dahanghai_entries_for_db
                    ]
                    await self._execute_sql(
                        fatal_sql.insert_dahanghai_list_table_sql,
                        executemany_params=dahanghai_entries_tuples
                    )
                    logger.info(f"Successfully inserted/updated {len(dahanghai_entries_tuples)} Dahanghai entries into database for {self.char_zh} ({self.mid}) on {current_time_str}.")
                except Exception as db_err:
                    logger.error(f"Database error inserting Dahanghai list for {self.char_zh} ({self.mid}): {db_err}")
            else:
                logger.info(f"No Dahanghai entries to insert into database for {self.char_zh} ({self.mid}) on {current_time_str}.")

        except bilibili_exceptions.ApiException as api_ex:
            logger.error(f"Bilibili API error fetching Dahanghai list for {self.char_zh} ({self.mid}): {api_ex}")
        except Exception as e:
            logger.error(f"Failed to get or process Dahanghai list for {self.char_zh} ({self.mid}): {e}")

    async def summarise_rise_reason(self, recent: int = 30):
        recent_info = await query_recent_info(self.mid, recent)
        rof = ReasoningOfFans(char=self.char_zh, llm_name=AI_GEN_MODEL)
        res = await rof.run(recent_info=recent_info)

        current_dt = datetime.datetime.now()
        current_ts = int(current_dt.timestamp())

        insert_data_dict = {
            "uid": self.mid,
            "name": self.char_zh,
            "cur_datetime": current_dt.replace(minute=0, second=0, microsecond=0),
            "cur_timestamp": current_ts,
            "recent": recent,
            # relationships, tieba_summaries, tieba_summaries_total are NULL in SQL
            "rise_reason": json.dumps(res, ensure_ascii=False),
        }
        # For VALUES ($1, $2, $3, $4, $5, NULL, NULL, NULL, $6)
        # Order for insert_or_update_rise_reason_sql
        insert_data_tuple = (
            insert_data_dict["uid"],
            insert_data_dict["name"],
            insert_data_dict["cur_datetime"],
            insert_data_dict["cur_timestamp"],
            insert_data_dict["recent"],
            insert_data_dict["rise_reason"], # This is $6 in the SQL
        )
        await self._execute_sql(fatal_sql.insert_or_update_rise_reason_sql, insert_data_tuple)
        logger.info(f"已将 {self.char_zh} 的粉丝增长原因总结存入 AI_GEN_TABLE")
        return res

    async def fetch_followers_list(self):
        """
        获取用户的完整粉丝列表，并将数据存入/更新到 followers_list_table 数据库。
        Fetches the complete follower list for the user and stores/updates it in the followers_list_table database.
        """
        logger.info(f"开始为用户 {self.char_zh} ({self.mid}) 获取并更新粉丝列表到数据库...")

        # 1. 确保 followers_list_table 存在
        # 注意：create_followers_list_table_sql 是一个字符串常量，不是函数
        try:
            logger.info(f"确保表 followers_list_table 存在...")
            await self._execute_sql(fatal_sql.create_followers_list_table_sql)
            logger.info(f"表 followers_list_table 已确保存在.")
        except Exception as e:
            logger.error(f"尝试确保表 followers_list_table 存在时出错: {e}\nSQL: {fatal_sql.create_followers_list_table_sql}")
            return # 如果无法确保表存在，则无法继续

        followers_to_insert_update = []
        api_follower_uids = set() # 用于跟踪从 API 获取到的所有粉丝 UID

        try:
            # 2. 获取粉丝总数
            # to_handle_follower_num = await query_current_follower_change_num(self.mid) # current not today
            to_handle_follower_num =2500
            logger.info(f"用户 {self.char_zh} ({self.mid}) 新增粉丝总数: {to_handle_follower_num}")

            if to_handle_follower_num <= 0:
                logger.info(f"用户 {self.char_zh} 没有增长粉丝，无需获取。")
                await self._execute_sql(fatal_sql.delete_followers_by_up_uid_sql, (self.mid,))
                return

            # 3. 分页获取粉丝列表
            page_size = 50
            total_pages = min(math.ceil(to_handle_follower_num / page_size), FETCH_FOLLOWERS_MAX_PAGES)
            logger.info(f"需要获取 {total_pages} 页粉丝数据。")

            for page in range(1, total_pages + 1):
                logger.info(f"获取粉丝列表第 {page} 页 / 共 {total_pages} 页...")
                try:
                    data = await self.u.get_followers(pn=page, ps=page_size)
                    if not data or "list" not in data:
                        logger.warning(f"获取粉丝列表第 {page} 页失败或返回数据格式错误。")
                        continue # 跳过此页

                    current_page_fans = data.get("list", [])
                    if not current_page_fans:
                        logger.info(f"第 {page} 页没有粉丝数据。")
                        # 理论上不应发生，除非粉丝数在获取过程中减少
                        continue

                    # 4. 处理当前页的粉丝数据
                    current_timestamp = datetime.datetime.now()
                    for fan in current_page_fans:
                        follower_uid = fan.get("mid")
                        if not follower_uid:
                            logger.warning(f"跳过缺少 mid 的粉丝数据: {fan}")
                            continue

                        api_follower_uids.add(str(follower_uid)) # 添加到集合

                        follow_time_ts = fan.get("mtime")
                        # follow_time_dt = datetime.datetime.fromtimestamp(follow_time_ts) if follow_time_ts else None
                        # PGSQL 的 bigint 可以直接存时间戳

                        follower_data = {
                            "up_uid": self.mid,
                            "follower_uid": str(follower_uid),
                            "follower_name": fan.get("uname", "").replace('\x00', ''), # 移除空字节
                            "face_url": fan.get("face"),
                            "sign": fan.get("sign", "").replace('\x00', ''), # 移除空字节
                            "follow_time": follow_time_ts, # 直接存储时间戳
                            "record_timestamp": current_timestamp # 记录获取时间
                        }
                        followers_to_insert_update.append(follower_data)

                    # 5. 批量插入/更新数据库 (每页处理完后执行一次)
                    if followers_to_insert_update:
                        logger.info(f"准备向数据库插入/更新 {len(followers_to_insert_update)} 条粉丝数据 (来自第 {page} 页)...")
                        try:
                            followers_ordered_keys = [
                                "up_uid", "follower_uid", "follower_name", "face_url", "sign",
                                "follow_time", "record_timestamp"
                            ]
                            followers_to_insert_tuples = [
                                tuple(d.get(key) for key in followers_ordered_keys) for d in followers_to_insert_update
                            ]
                            await self._execute_sql(
                                fatal_sql.insert_follower_sql,
                                executemany_params=followers_to_insert_tuples
                            )
                            logger.info(f"成功插入/更新 {len(followers_to_insert_tuples)} 条粉丝数据。")
                            followers_to_insert_update = [] # 清空列表
                        except Exception as insert_err:
                            logger.error(f"插入/更新粉丝数据到数据库时出错: {insert_err}")
                            followers_to_insert_update = [] # 清空以避免重试错误数据

                    await asyncio.sleep(FOLLOWER_LIST_FETCH_LIST_DELAY) # API 调用间隔

                except bilibili_exceptions.ApiException as api_ex:
                    logger.error(f"调用 Bilibili API 获取粉丝列表第 {page} 页时出错: {api_ex}")
                    await asyncio.sleep(FOLLOWER_LIST_FETCH_LIST_DELAY)
                    break # TODO
                    # 可以选择重试或跳过
                except Exception as page_err:
                    logger.error(f"处理粉丝列表第 {page} 页时发生意外错误: {page_err}")
                    await asyncio.sleep(FOLLOWER_LIST_FETCH_LIST_DELAY)
                    break # TODO
                    # 跳过此页

            # --- 可选：删除不再关注的粉丝 ---
            if DELETE_UNFOLLOWED_FANS:
                db_followers_sql = "SELECT follower_uid FROM followers_list_table WHERE up_uid = $1"
                db_results = await self._execute_sql(db_followers_sql, (self.mid,), fetch_all=True)
                db_follower_uids = {str(row['follower_uid']) for row in db_results} if db_results else set()

                uids_to_delete = db_follower_uids - api_follower_uids
                if uids_to_delete:
                    logger.info(f"发现 {len(uids_to_delete)} 个已取关的粉丝，准备从数据库删除...")
                    try:
                        delete_sql = "DELETE FROM followers_list_table WHERE up_uid = $1 AND follower_uid = ANY($2::text[])"
                        await self._execute_sql(delete_sql, (self.mid, list(uids_to_delete)))
                        logger.info(f"成功删除 {len(uids_to_delete)} 个已取关的粉丝记录。")
                    except Exception as delete_err:
                        logger.error(f"删除已取关粉丝时出错: {delete_err}")
                else:
                    logger.info("没有检测到需要删除的已取关粉丝记录。")
            # --- 可选删除结束 ---

            logger.info(f"用户 {self.char_zh} ({self.mid}) 的粉丝列表数据库同步完成。")

        except bilibili_exceptions.ApiException as api_ex:
            logger.error(f"调用 Bilibili API 获取粉丝总数时出错: {api_ex}")
        except Exception as e:
            logger.error(f"获取或处理用户 {self.char_zh} ({self.mid}) 粉丝列表时发生意外错误: {e}")

    async def fetch_follower_review(self, target_date: str, recent: int = 7):
        """
        根据指定日期和近期天数，获取新粉丝的互动情况并存入数据库。
        1. 获取 target_date 前 recent 天内的新粉丝 (follower_uid, follower_name) 从 followers_list_table。
        2. 对每个新粉丝，检查其 uid 是否在 target_date 前 recent 天内出现在 enter_room_user_info_{live_id} 表中。
        3. 将结果 (up_uid, up_name, query_date, recent_days, follower_uid, follower_name, if_review) 存入 follower_review_table。
        """
        logger.info(f"开始为 {self.char_zh} ({self.mid}) 获取 {target_date} 前 {recent} 天的粉丝互动情况...")

        try:
            target_date_obj = datetime.datetime.strptime(target_date, "%Y-%m-%d").date()
        except ValueError:
            logger.error(f"无效的日期格式: {target_date}. 请使用 YYYY-MM-DD 格式。")
            return

        if not self.liveid:
            logger.warning(f"liveid for {self.char_zh} not set before fetch_follower_review. Attempting async_init.")
            await self.async_init()

        if not self.liveid:
            logger.error(f"无法获取 {self.char_zh} 的 liveid，无法继续 fetch_follower_review。")
            return

        # 1. 获取近期新粉丝
        # 定义日期范围: recent 天，包含 target_date
        end_dt_query = datetime.datetime.combine(target_date_obj, datetime.time.max)
        start_dt_query = datetime.datetime.combine(target_date_obj - datetime.timedelta(days=recent - 1), datetime.time.min)
        
        start_ts_followers = int(start_dt_query.timestamp())
        end_ts_followers = int(end_dt_query.timestamp())

        recent_followers_sql = """
            SELECT follower_uid, follower_name
            FROM followers_list_table
            WHERE up_uid = $1 AND follow_time >= $2 AND follow_time <= $3
        """
        recent_followers_results = await self._execute_sql(
            recent_followers_sql,
            (self.mid, start_ts_followers, end_ts_followers),
            fetch_all=True
        )

        if not recent_followers_results:
            logger.info(f"在 {target_date} 前 {recent} 天内没有找到 {self.char_zh} 的新粉丝。")
            return

        logger.info(f"找到 {len(recent_followers_results)} 个新粉丝进行互动检查。")

        reviews_to_insert = []
        enter_room_table_name = sql_const.get_enter_room_user_info_table_name(self.liveid)
        
        table_exists_result = await self.table_exists(enter_room_table_name.split('.')[-1])
        if not table_exists_result:
            logger.warning(f"表 {enter_room_table_name} 不存在，无法检查粉丝互动情况。")
            # 根据需求，可以选择不插入任何数据，或者将所有 if_review 设为 False
            # 这里选择不插入，因为无法确认互动状态
            return

        for follower_row in recent_followers_results:
            follower_uid = follower_row["follower_uid"]
            follower_name = follower_row["follower_name"]
            if_review_bool = False

            # 2. 检查粉丝是否在近期进入过直播间
            check_enter_room_sql = f"""
                SELECT 1
                FROM {enter_room_table_name}
                WHERE uid = $1 AND timestamp >= $2 AND timestamp <= $3
                LIMIT 1
            """
            try:
                enter_room_entry = await self._execute_sql(
                    check_enter_room_sql,
                    (follower_uid, start_ts_followers, end_ts_followers), 
                    fetch_one=True
                )
                if enter_room_entry:
                    if_review_bool = True
            except Exception as e:
                # 如果表不存在或查询失败，记录错误并默认 if_review 为 False
                logger.error(f"查询表 {enter_room_table_name} 时出错 (UID: {follower_uid}): {e}. 默认 if_review 为 False。")
                if_review_bool = None


            review_data = {
                "up_uid": self.mid,
                "up_name": self.char_zh,
                "query_date": target_date_obj,
                "recent_days": recent,
                "follower_uid": follower_uid,
                "follower_name": follower_name,
                "if_review": if_review_bool
            }
            reviews_to_insert.append(review_data)

            # 4. 批量插入
            if len(reviews_to_insert) >= FOLLOWER_REVIEW_INSERT_POOL_SIZE:
                logger.info(f"向 follower_review_table 批量插入 {len(reviews_to_insert)} 条数据...")
                follower_review_ordered_keys = [
                    "up_uid", "up_name", "query_date", "recent_days", "follower_uid", 
                    "follower_name", "if_review"
                ]
                await self._batch_insert_data("follower_review_table", fatal_sql.insert_or_update_follower_review_sql, reviews_to_insert, follower_review_ordered_keys)
                reviews_to_insert = []

        # 插入剩余数据
        if reviews_to_insert:
            logger.info(f"向 follower_review_table 批量插入剩余 {len(reviews_to_insert)} 条数据...")
            follower_review_ordered_keys = [
                "up_uid", "up_name", "query_date", "recent_days", "follower_uid", 
                "follower_name", "if_review"
            ]
            await self._batch_insert_data("follower_review_table", fatal_sql.insert_or_update_follower_review_sql, reviews_to_insert, follower_review_ordered_keys)

        logger.info(f"为 {self.char_zh} ({self.mid}) 在 {target_date} 的粉丝互动情况处理完成。")

    async def get_live_id(self):
        # Attempt to read from vups.json first
        try:
            from backend.utils.utils import get_vup_room_id_by_short_name
            room_id = get_vup_room_id_by_short_name(self.char_zh)
            if room_id is not None:
                liveid_from_config = str(room_id)
                logger.info(f"Found live ID {liveid_from_config} for {self.char_zh} in vups.json")
                return liveid_from_config
        except Exception as e:
            logger.warning(f"Failed to read vups.json: {e}")

        # If not found in config or config read error, fetch from API
        logger.info(f"Live ID for {self.char_zh} not found in vups.json or error reading. Fetching from API.")
        try:
            room_id_from_api = await self.fetch_user_info() # fetch_user_info is async and returns room_id
            if room_id_from_api:
                fetched_liveid = str(room_id_from_api)
                logger.info(f"Fetched live ID {fetched_liveid} for {self.char_zh} from API.")
                # Note: In the new system, we don't automatically save to file
                # The vups.json should be manually maintained
                return fetched_liveid
            else:
                logger.error(f"Failed to fetch live ID for {self.char_zh} from API (fetch_user_info returned None).")
                return None
        except Exception as e:
            logger.error(f"Failed to get or save live ID for {self.char_zh} via API: {e}")
            return None

    async def fetch_history_follower_and_dahanghai(self):
        """
        Fetches historical follower and Dahanghai data from vtbs.moe API
        and stores it into the current_stat_table.
        This function is intended to run once. If data older than one year
        already exists for the user, it will not proceed.
        """
        logger.info(f"[{self.char_zh}] Starting to fetch historical follower and Dahanghai data.")

        # 1. Check if data older than one year already exists
        one_year_ago_dt = datetime.datetime.now() - datetime.timedelta(days=365)
        one_year_ago_ts = int(one_year_ago_dt.timestamp())

        check_sql = "SELECT MIN(timestamp) as min_ts FROM current_stat_table WHERE uid = $1"
        try:
            oldest_record = await self._execute_sql(check_sql, (self.mid,), fetch_one=True)
            if oldest_record and oldest_record['min_ts'] and oldest_record['min_ts'] < one_year_ago_ts:
                logger.info(f"[{self.char_zh}] Historical data older than one year already exists for UID {self.mid}. Skipping fetch.")
                return
        except Exception as e:
            logger.error(f"[{self.char_zh}] Error checking for existing historical data: {e}")
            # Decide if to proceed or not. For safety, let's return if check fails.
            return

        merged_data = {} # Stores {datetime_sec: {'follower': count, 'dahanghai': count}}
        records_to_insert = []

        async with aiohttp.ClientSession() as session:
            # 2. Fetch follower data
            url_follower = f"https://api.vtbs.moe/v2/bulkActive/{self.mid}"
            try:
                logger.info(f"[{self.char_zh}] Fetching historical follower data from {url_follower}")
                async with session.get(url_follower) as response:
                    response.raise_for_status()
                    follower_history = await response.json()
                    if isinstance(follower_history, list):
                        for item in follower_history:
                            if 'time' in item and 'follower' in item:
                                ts_sec = item['time'] / 1000
                                dt_obj = datetime.datetime.fromtimestamp(ts_sec).replace(minute=0, second=0, microsecond=0)
                                merged_data.setdefault(dt_obj, {})['follower'] = item['follower']
                        logger.info(f"[{self.char_zh}] Fetched {len(follower_history)} follower data points.")
                    else:
                        logger.warning(f"[{self.char_zh}] Unexpected format for follower history: {type(follower_history)}")
            except aiohttp.ClientError as e:
                logger.error(f"[{self.char_zh}] HTTP error fetching follower data: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"[{self.char_zh}] JSON decode error for follower data: {e}")
            except Exception as e:
                logger.error(f"[{self.char_zh}] Unexpected error fetching follower data: {e}")

            # 3. Fetch dahanghai data
            url_dahanghai = f"https://api.vtbs.moe/v2/bulkGuard/{self.mid}"
            try:
                logger.info(f"[{self.char_zh}] Fetching historical Dahanghai data from {url_dahanghai}")
                async with session.get(url_dahanghai) as response:
                    response.raise_for_status()
                    dahanghai_history = await response.json()
                    if isinstance(dahanghai_history, list):
                        for item in dahanghai_history:
                            if 'time' in item and 'guardNum' in item:
                                ts_sec = item['time'] / 1000
                                dt_obj = datetime.datetime.fromtimestamp(ts_sec).replace(minute=0, second=0, microsecond=0)
                                merged_data.setdefault(dt_obj, {})['dahanghai'] = item['guardNum']
                        logger.info(f"[{self.char_zh}] Fetched {len(dahanghai_history)} Dahanghai data points.")
                    else:
                        logger.warning(f"[{self.char_zh}] Unexpected format for Dahanghai history: {type(dahanghai_history)}")
            except aiohttp.ClientError as e:
                logger.error(f"[{self.char_zh}] HTTP error fetching Dahanghai data: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"[{self.char_zh}] JSON decode error for Dahanghai data: {e}")
            except Exception as e:
                logger.error(f"[{self.char_zh}] Unexpected error fetching Dahanghai data: {e}")

        # 4. Prepare data for DB insertion
        if not merged_data:
            logger.info(f"[{self.char_zh}] No historical data fetched or processed. Nothing to insert.")
            return

        for dt_obj, data_counts in merged_data.items():
            ts_sec = int(dt_obj.timestamp())
            record = {
                "uid": self.mid,
                "name": self.char_zh,
                "timestamp": ts_sec,
                "datetime": dt_obj,
                "follower_num": data_counts.get('follower'), # Will be None if not present
                "dahanghai_num": data_counts.get('dahanghai'), # Will be None if not present
                "video_total_num": None,
                "article_total_num": None,
                "likes_total_num": None,
                "elec_num": None,
            }
            records_to_insert.append(record)

        # 5. Batch insert into current_stat_table
        if records_to_insert:
            logger.info(f"[{self.char_zh}] Preparing to insert/update {len(records_to_insert)} historical stat records.")
            try:
                current_stat_ordered_keys = [
                    "uid", "name", "timestamp", "datetime", "follower_num", "dahanghai_num",
                    "video_total_num", "article_total_num", "likes_total_num", "elec_num"
                ]
                records_to_insert_tuples = [
                    tuple(d.get(key) for key in current_stat_ordered_keys) for d in records_to_insert
                ]
                await self._execute_sql(
                    fatal_sql.insert_current_stat_table_sql,
                    executemany_params=records_to_insert_tuples
                )
                logger.info(f"[{self.char_zh}] Successfully inserted/updated {len(records_to_insert_tuples)} historical stat records.")
            except Exception as db_err:
                logger.error(f"[{self.char_zh}] Database error inserting historical stat records: {db_err}")
        else:
            logger.info(f"[{self.char_zh}] No records to insert after processing.")

        logger.info(f"[{self.char_zh}] Finished fetching historical follower and Dahanghai data.")
        asyncio.sleep(FETCH_HISTORY_F_AND_D_DELAY)


def get_vtuber_list():
    from backend.utils.utils import get_vtuber_list as get_vtuber_list_from_config
    return get_vtuber_list_from_config()

def get_vtubers_server_instance():
    vtuber_server_instance: Dict[str, UserInfoServer] = {}
    for vtuber in get_vtuber_list():
        vtuber_server_instance[vtuber] = UserInfoServer(vtuber)
    return vtuber_server_instance

vtuberUserInfoServer = get_vtubers_server_instance()

async def user_info_1_minute_worker():    
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                # Add minute tasks here if any
                pass
            else:
                logger.warning(f"Skipping 1minute_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_1_minute_worker for {vtuber}: {e}")
            continue

async def user_info_5_minute_worker():    
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                # Add minute tasks here if any
                pass
            else:
                logger.warning(f"Skipping 5minute_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_5_minute_worker for {vtuber}: {e}")
            continue

async def user_info_hour_worker():
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                await server.fetch_user_current_stat()
            else:
                logger.warning(f"Skipping hour_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_hour_worker for {vtuber}: {e}")
            continue

async def user_info_1_day_worker():
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                cur_date_str = datetime.datetime.now().strftime("%Y-%m-%d")
                await server.fetch_user_dynamics()
                await server.fetch_all_video()
                await server.fetch_dahanghai_list()
                await server.fetch_followers_list()
                await server.fetch_follower_review(cur_date_str, 7)
            else:
                logger.warning(f"Skipping 1day_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_1_day_worker for {vtuber}: {e}")
            continue

async def user_info_3_day_worker():
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                await server.gen_recent_relationships(30)
                await server.gen_recent_relationships(100)
                await server.gen_comment_topics(30)
                await server.summarise_rise_reason(7)
            else:
                logger.warning(f"Skipping 3day_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_3_day_worker for {vtuber}: {e}")
            continue

async def user_info_week_worker():
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                await server.fetch_fans_medal_rank()
                await server.fetch_user_info()
            else:
                logger.warning(f"Skipping week_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_week_worker for {vtuber}: {e}")
            continue

async def user_info_month_worker():
    for vtuber, server in vtuberUserInfoServer.items():
        try:
            if server.liveid is None: await server.async_init()
            if server.liveid:
                await server.fetch_history_follower_and_dahanghai()
            else:
                logger.warning(f"Skipping month_worker for {vtuber} as liveid is not available.")
        except Exception as e:
            logger.error(f"Error in user_info_month_worker for {vtuber}: {e}")
            continue

async def user_info_till_worker():
    for vtuber, server in vtuberUserInfoServer.items():
        if server.liveid is None: await server.async_init()
        if server.liveid:
            try:
                await server.fetch_all_dynamics_comments(interval_days=7)
                await server.fetch_all_videos_comments(interval_days=7)
                await server.gen_comment_sensiment()
                await server.fetch_tieba_threads()
                await server.fetch_tieba_whole()
            except Exception as e:
                logger.error(f"Error in user_info_till_worker for {vtuber}: {e}")
                continue
        else:
            logger.warning(f"Skipping till_worker for {vtuber} as liveid is not available.")
