import requests
import os
import time
import json
from const import (
    PROJECT_ROOT,
)
import backend.utils as U
from logger import logger


class FetchUkaApi:
    def __init__(self, char):
        self.char = char
        self.set_char(self.char)

    def set_char(self, char):
        self.char_zh = U.get_zh_role_name(char)
        self.all_dir = f"{PROJECT_ROOT}/output/{self.char_zh}"
        os.makedirs(self.all_dir, exist_ok=True)
        self.live_dir_path = f"{PROJECT_ROOT}/output/{self.char_zh}/Live"
        os.makedirs(self.live_dir_path, exist_ok=True)

        self.channel_path = f"{self.live_dir_path}/channel.json"

        self.stream_dir = f"{self.live_dir_path}/stream"
        os.makedirs(self.stream_dir, exist_ok=True)
        self.mid = self.get_user_mid()

    def get_user_mid(self):
        uid = U.get_vup_uid_by_short_name(self.char_zh)
        if uid is None:
            raise Exception(f"Character {self.char_zh} not found in vups.json")
        return str(uid)

    async def fetch_channel(self):
        logger.info("RUNNING: fetch_channel_from_uka")
        url = (
            f"https://api.ukamnads.icu/api/v2/channel?includeExtra=true&uid={self.mid}"
        )
        # current_time = time.strftime("%Y-%m-%d %H")
        current_time = time.strftime("%Y-%m-%d")
        res_content = {}
        if os.path.exists(self.channel_path):
            with open(self.channel_path, "r", encoding="utf-8") as f:
                checkcontent = json.loads(f.read())
                if checkcontent["time"] == current_time:
                    return checkcontent
        try:
            response = requests.get(url, timeout=None)
            response.raise_for_status()
            res_content = response.json()
            res_content["time"] = current_time

            with open(self.channel_path, "w", encoding="utf-8") as f:
                f.write(json.dumps(res_content, ensure_ascii=False, indent=4))
        except Exception as e:
            logger.error(f"Error happens when fetching total live status from uka: {e}")

        return res_content

    async def fetch_stream(self, liveID):
        logger.info("RUNNING: fetch_stream_from_uka")
        url = f"https://api.ukamnads.icu/api/v2/live?includeExtra=true&liveId={liveID}"

        current_time = time.strftime("%Y-%m-%d")
        res_content = {}
        stream_path = f"{self.live_dir_path}/stream.json"
        if os.path.exists(stream_path):
            with open(stream_path, "r", encoding="utf-8") as f:
                checkcontent = json.loads(f.read())
                if checkcontent["time"] == current_time:
                    logger.info(
                        f"RUNNING: fetch_stream_from_uka: {checkcontent['time']}"
                    )
                    return checkcontent
        try:
            response = requests.get(url, timeout=None)
            response.raise_for_status()
            res_content = response.json()

            with open(stream_path, "w", encoding="utf-8") as f:
                f.write(json.dumps(res_content, ensure_ascii=False, indent=4))
        except Exception as e:
            logger.error(f"Error happens when fetching total live status from UKA: {e}")

        return res_content

    async def fetch_stream_cashed(self, liveID):  # TODO: with dammuku recovery
        logger.info("RUNNING: fetch_stream_from_uka")
        url = f"https://api.ukamnads.icu/api/v2/live?includeExtra=true&liveId={liveID}"

        stream_path = f"{self.stream_dir}/{liveID}.json"
        if os.path.exists(stream_path):
            with open(stream_path, "r", encoding="utf-8") as f:
                checkcontent = json.loads(f.read())
                return checkcontent
        try:
            response = requests.get(url, timeout=None)
            response.raise_for_status()
            res_content = response.json()

            with open(stream_path, "w", encoding="utf-8") as f:
                f.write(json.dumps(res_content, ensure_ascii=False, indent=4))
        except Exception as e:
            logger.error(f"Error happens when fetching total live status from UKA: {e}")

        return res_content
