from pypinyin import pinyin, <PERSON>


def convert_to_pinyin(text):
    return "".join([item[0] for item in pinyin(text, style=Style.NORMAL)])


def get_en_role_name(role_name):
    from backend.utils.utils import get_vup_en_name_by_short_name
    en_name = get_vup_en_name_by_short_name(role_name)
    return en_name if en_name else "xingtong"


def get_zh_role_name(role_name):
    from backend.utils.utils import read_vups_config
    vups = read_vups_config()

    for vup in vups:
        if vup["shortName"] == role_name:
            return role_name

    for vup in vups:
        if vup["enName"] == role_name:
            return vup["shortName"]

    return NotImplementedError


# input role_name , nick name is also allowed
# output folder_role_name and url url = f'https://github.com/LC1332/Haruhi-2-Dev/raw/main/data/character_in_zip/{role_name}.zip'
def get_folder_role_name(role_name):
    en_name = get_en_role_name(role_name)
    if en_name != "xingtong":  # Found the role
        folder_role_name = en_name
        url = f"https://github.com/LC1332/Haruhi-2-Dev/raw/main/data/character_in_zip/{folder_role_name}.zip"
        return folder_role_name, url
    else:
        print("role_name {} not found, using xingtong as default".format(role_name))
        return "xingtong", f"https://github.com/LC1332/Haruhi-2-Dev/raw/main/data/character_in_zip/xingtong.zip"


def get_mid_with_role_name(role_name):
    from backend.utils.utils import get_vup_uid_by_short_name
    uid = get_vup_uid_by_short_name(role_name)
    if uid is None:
        raise Exception(f"Character {role_name} not found in vups.json")
    return str(uid)
